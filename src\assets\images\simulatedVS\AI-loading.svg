<?xml version="1.0" encoding="UTF-8"?>
<svg xml:space="preserve" viewBox="0 0 100 100" y="0px" x="0px" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" id="圖層_1" version="1.1" style="margin: initial; display: block; shape-rendering: auto; background: rgb(255, 255, 255);" preserveAspectRatio="xMidYMid" width="200" height="200"><g class="ldl-scale" style="transform-origin: 50% 50%; transform: rotate(0deg) scale(0.8, 0.8);"><g class="ldl-ani">
    <g class="ldl-layer"><g class="ldl-ani" style="opacity: 1; transform-origin: 50px 50px; transform: matrix3d(0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 1); animation: 1s linear -0.625s infinite normal forwards running animate; transform-box: view-box;"><ellipse ry="43" rx="18" cy="50" cx="50" stroke-miterlimit="10" stroke-width="3.5" stroke="#333333" fill="none" transform="matrix(0.8605 -0.5094 0.5094 0.8605 -18.497 32.4456)" style="stroke-width: 3.5; stroke: rgb(23, 114, 246);"></ellipse></g></g>
    <g class="ldl-layer"><g class="ldl-ani" style="opacity: 1; transform-origin: 50px 50px; transform: matrix3d(0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 1); animation: 1s linear -0.75s infinite normal forwards running animate; transform-box: view-box;"><ellipse ry="18" rx="43" cy="50" cx="50" stroke-miterlimit="10" stroke-width="3.5" stroke="#333333" fill="none" transform="matrix(0.4905 -0.8714 0.8714 0.4905 -18.0974 69.0459)" style="stroke-width: 3.5; stroke: rgb(23, 114, 246);"></ellipse></g></g>
    <g class="ldl-layer"><g class="ldl-ani" style="opacity: 1; transform-origin: 50px 50px; transform: matrix3d(0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 1); animation: 1s linear -0.875s infinite normal forwards running animate; transform-box: view-box;"><ellipse ry="18" rx="43" cy="50" cx="50" stroke-miterlimit="10" stroke-width="3.5" stroke="#333333" fill="none" transform="matrix(0.9999 -1.091849e-02 1.091849e-02 0.9999 -0.5429 0.5489)" style="stroke-width: 3.5; stroke: rgb(23, 114, 246);"></ellipse></g></g>
    <g class="ldl-layer"><g class="ldl-ani" style="opacity: 1; transform-origin: 50px 50px; transform: matrix3d(0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 0.91, 0, 0, 0, 0, 1); animation: 1s linear -1s infinite normal forwards running animate; transform-box: view-box;"><circle r="6.2" cy="50" cx="50" stroke-miterlimit="10" stroke-width="3.5" stroke="#E15B64" fill="none" style="stroke-width: 3.5; stroke: rgb(23, 114, 246);"></circle></g></g>
    </g></g>
</svg>