<template>
    <div class="publicComment">
        <el-form ref="form" :model="commentForm" :rules="rules">
            <el-form-item label="" prop="instructionId">
                <el-select v-model="commentForm.instructionId" placeholder="请选择指令" clearable filterable
                    style="width: 100%">
                    <el-option v-for="item in instructionList" :key="item.value" :value="item.value"
                        :label="`指令${item.commentOrder}： ${item.label}`">
                    </el-option>
                </el-select>
            </el-form-item>


            <div class="tampTab">
                <div class="tabTitle">选择评论模版</div>
                <div class="tabMain">
                    <div v-for="item in templateList" :class="{'activeTab':commentForm.commentType==item.value}"
                        @click="commentForm.commentType=item.value" :key="item.value">{{ item.label }}</div>
                </div>
            </div>

            <!-- 热搜榜 -->
            <template v-if="commentForm.commentType=='1'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model.trim="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入热搜榜内容" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
                <el-form-item prop="rank">
                    <div class="formLabel">热搜排名</div>
                    <el-input-number v-model="commentForm.rank" controls-position="right" :min="1" :max="50"
                        style="width: 120px;"></el-input-number>
                </el-form-item>
                <el-form-item prop="hotNum">
                    <div class="formLabel">热度值</div>
                    <el-input-number v-model="commentForm.hotNum" controls-position="right" :min="1" :max="999999"
                        clearable style="width: 120px;"></el-input-number>
                </el-form-item>
                <el-form-item prop="rankType">
                    <div class="formLabel">文章类型</div>
                    <el-select v-model="commentForm.rankType" clearable>
                        <el-option label="热" value="热"></el-option>
                        <el-option label="新" value="新"></el-option>
                    </el-select>
                </el-form-item>
            </template>

            <!-- 热议话题 -->
            <!-- <template v-if="commentForm.commentType=='2'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model.trim="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入热议话题" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
            </template> -->

            <!-- 爆料贴文 -->
            <template v-if="commentForm.commentType=='2'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入爆料贴文" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
                <el-upload ref="upload" class="upload-file-uploader" :headers="headers" :action="uploadFileUrl"
                    :file-list="fileList" :before-upload="handleBeforeUpload" :on-success="handleFileSuccess"
                    :on-remove="handleFileRemove" :on-exceed="handleExceed" :show-file-list="true" :accept="acceptList">
                    <div class="upload-btn">
                        <div class="upload-btn-item" @click="uploadImg">
                            <img src="@/assets/images/simulatedVS/pictureIcon.png" alt="">图片
                        </div>
                        <div class="upload-btn-item" @click="uploadVideo">
                            <img src="@/assets/images/simulatedVS/videoIcon.png" alt="">视频
                            <el-tooltip class="item" effect="dark" :content="`视频支持${videoTypes}格式`"
                                placement="top-start">
                                <i style="margin-left: 5px;" class="el-icon-question"></i>
                            </el-tooltip>
                        </div>
                    </div>
                </el-upload>
                <el-form-item v-if="commentForm.commentType=='2'" label="" prop="relatedCommentType"
                    style="margin-top: 20px;">
                    <div class="formLabel">关联内容</div>
                    <el-select v-model="commentForm.relatedCommentType" placeholder="请选择关联内容" clearable filterable>
                        <el-option label="情况通报" :value="4"></el-option>
                        <el-option label="分析报告" :value="5"></el-option>
                    </el-select>
                </el-form-item>
            </template>


            <!-- 情况通报 -->
            <template v-if="commentForm.commentType=='4'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入通报" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
                <el-upload ref="uploadDoc4" class="upload-file-uploader" :headers="headers" :action="parseFileUrl"
                    :show-file-list="true" :limit="1" accept=".doc,.docx" :before-upload="handleDocBeforeUpload"
                    :on-exceed="handleDocExceed" :on-success="handleDocSuccess">
                    <div class="upload-btn">
                        <div class="upload-btn-item">
                            <i class="el-icon-document" style="font-size: 17px;margin-right: 5px;"></i>
                            <!-- <img src="@/assets/images/simulatedVS/videoIcon.png" alt=""> -->
                            上传文档
                        </div>
                    </div>
                </el-upload>
            </template>

            <!-- 警情通报 -->
            <!-- <template v-if="commentForm.commentType=='5'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model.trim="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入通报" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
            </template> -->

            <!-- 分析报告 -->
            <template v-if="commentForm.commentType=='5'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入通报" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
                <el-upload ref="uploadDoc5" class="upload-file-uploader" :headers="headers" :action="parseFileUrl"
                    :show-file-list="true" :limit="1" accept=".doc,.docx" :before-upload="handleDocBeforeUpload"
                    :on-exceed="handleDocExceed" :on-success="handleDocSuccess">
                    <div class="upload-btn">
                        <div class="upload-btn-item">
                            <i class="el-icon-document" style="font-size: 17px;margin-right: 5px;"></i>
                            <!-- <img src="@/assets/images/simulatedVS/videoIcon.png" alt=""> -->
                            上传文档
                        </div>
                    </div>
                </el-upload>
            </template>

            <!-- 文件函 -->
            <template v-if="commentForm.commentType=='9'">
                <el-form-item prop="title">
                    <el-input v-model="commentForm.title" maxlength="20" show-word-limit placeholder="请输入文件函标头"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item prop="content">
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入文件函内容" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>
                <el-upload ref="uploadDoc5" class="upload-file-uploader" :headers="headers" :action="parseFileUrl"
                    :show-file-list="true" :limit="1" accept=".doc,.docx" :before-upload="handleDocBeforeUpload"
                    :on-exceed="handleDocExceed" :on-success="handleDocSuccess">
                    <div class="upload-btn">
                        <div class="upload-btn-item">
                            <i class="el-icon-document" style="font-size: 17px;margin-right: 5px;"></i>
                            <!-- <img src="@/assets/images/simulatedVS/videoIcon.png" alt=""> -->
                            上传文档
                        </div>
                    </div>
                </el-upload>
            </template>


            <!-- 普通评论 -->
            <template v-if="commentForm.commentType=='3'">
                <el-form-item prop="content">
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="请输入评论" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>

                <el-upload ref="upload" class="upload-file-uploader" :headers="headers" :action="uploadFileUrl"
                    :file-list="fileList" :before-upload="handleBeforeUpload" :on-success="handleFileSuccess"
                    :on-remove="handleFileRemove" :on-exceed="handleExceed" :show-file-list="true" :accept="acceptList">
                    <div class="upload-btn">
                        <div class="upload-btn-item" @click="uploadImg">
                            <img src="@/assets/images/simulatedVS/pictureIcon.png" alt="">图片
                        </div>
                        <div class="upload-btn-item" @click="uploadVideo">
                            <img src="@/assets/images/simulatedVS/videoIcon.png" alt="">视频
                            <el-tooltip class="item" effect="dark" :content="`视频支持${videoTypes}格式`"
                                placement="top-start">
                                <i style="margin-left: 5px;" class="el-icon-question"></i>
                            </el-tooltip>
                        </div>
                    </div>
                </el-upload>

            </template>



            <!-- 衍生话题 -->
            <template v-if="commentForm.commentType=='10'">

                <el-form-item label="" prop="hotTopicCommentId" style="margin-top: 20px;">
                    <div class="formLabel">关联贴文</div>
                    <el-select v-model="commentForm.hotTopicCommentId" placeholder="请选择关联贴文" clearable filterable
                        @focus="getPostList">
                        <el-option v-for="item in postsList" :key="item.value" :value="item.value"
                            :label="item.label"></el-option>
                    </el-select>
                </el-form-item>

                <div style="display: flex; justify-content: flex-end;margin-bottom: 0px;">
                    <el-button :loading="AILoading" size="mini" type="primary" @click="getAIRelatedTopics">
                        <span style="display: flex;justify-content: space-between;align-items: center;">
                            <img style="margin-right: 5px;" src="@/assets/images/simulatedVS/AI-btn.svg" alt="">
                            AI一键生成
                        </span>
                    </el-button>
                </div>

                <el-form-item prop="AI-content">
                    <div class="formLabel"
                        style="width: 100%;display: flex; align-items: center;justify-content: space-between;">
                        <span>AI衍生话题</span>
                        <span class="refresh-btn" @click=""><i class="el-icon-refresh-left"></i> 换一换</span>
                    </div>
                    <div v-if="!AILoading" class="AI-tags">
                        <img src="@/assets/images/simulatedVS/AI-loading.svg" alt="">
                        <div class="AI-tags-item" v-for="item in AITagList" :key="item.tagName">
                            <span class="AI-tag">#<span>{{ item.tagName }}</span>#</span>
                            <div class="publish-btn" @click="">
                                <img src="@/assets/images/simulatedVS/paperAirplane.svg" alt="">发布
                            </div>
                        </div>
                    </div>
                    <div v-else class="AI-tags AI-loading">
                        {{ AITagText }}
                    </div>
                </el-form-item>

                <el-form-item prop="content">
                    <div class="formLabel">输入话题</div>
                    <el-input type="textarea" v-model="commentForm.content" maxlength="10000" show-word-limit
                        placeholder="如AI生成不满意，可在此输入框中人工输入" :autosize="{ minRows: 4, maxRows: 6}" style="width: 100%" />
                </el-form-item>

                <el-upload ref="upload" class="upload-file-uploader" :headers="headers" :action="uploadFileUrl"
                    :file-list="fileList" :before-upload="handleBeforeUpload" :on-success="handleFileSuccess"
                    :on-remove="handleFileRemove" :on-exceed="handleExceed" :show-file-list="true" :accept="acceptList">
                    <div class="upload-btn">
                        <div class="upload-btn-item" @click="uploadImg">
                            <img src="@/assets/images/simulatedVS/pictureIcon.png" alt="">图片
                        </div>
                        <div class="upload-btn-item" @click="uploadVideo">
                            <img src="@/assets/images/simulatedVS/videoIcon.png" alt="">视频
                            <el-tooltip class="item" effect="dark" :content="`视频支持${videoTypes}格式`"
                                placement="top-start">
                                <i style="margin-left: 5px;" class="el-icon-question"></i>
                            </el-tooltip>
                        </div>
                    </div>
                </el-upload>
            </template>

        </el-form>
        <div style="display: flex; justify-content: flex-end;margin-bottom: 20px;">
            <el-button size="small" @click="resetForm">置空</el-button>
            <el-button :disabled="submitDisable" :loading="submitLoading" size="mini" type="primary"
                @click="submitComment">发布</el-button>
        </div>


        <div class="templatePreview">
            <div class="previewTitle">模版预览</div>
            <img v-show="commentForm.commentType=='1'" src="@/assets/images/simulatedVS/hotSearchTemp.png" alt="">
            <!-- <img v-show="commentForm.commentType=='2'" src="@/assets/images/simulatedVS/hotTalkTemp.png" alt=""> -->
            <img v-show="commentForm.commentType=='2'" src="@/assets/images/simulatedVS/bltwTemp.png" alt="">
            <img v-show="commentForm.commentType=='4'" src="@/assets/images/simulatedVS/qinkuangTemp.svg" alt="">
            <!-- <img v-show="commentForm.commentType=='5'" src="@/assets/images/simulatedVS/jingqinTemp.png" alt=""> -->
            <img v-show="commentForm.commentType=='5'" src="@/assets/images/simulatedVS/fxbgTemp.svg" alt="">
            <img v-show="commentForm.commentType=='9'" src="@/assets/images/simulatedVS/hanTemp.svg" alt="">
            <img v-show="commentForm.commentType=='10'" src="@/assets/images/simulatedVS/yshtTemp.png" alt="">
            <img v-show="commentForm.commentType=='3'" src="@/assets/images/simulatedVS/normalTemp.png" alt="">
        </div>
    </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { fileGetUrlByIdsApi, drillCommentQueryApi, drillCommentPublishApi, AIRelatedTopicsApi } from "@/api/simulatedVS/index.js";
import { WS_EVENTS, safeOn } from '@/utils/eventBus'
export default {
    props: {
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        detailInfo: {
            type: Object,
            default: () => {
                return {
                    roleInfo: '',
                }
            }
        },
    },
    data() {
        return {
            headers: { Authorization: "Bearer " + getToken() },
            uploadFileUrl: process.env.VUE_APP_BASE_API + "/file/uploadFile", // 上传的图片服务器地址
            parseFileUrl: process.env.VUE_APP_BASE_API + "/fileOperate/importDoc", // 文档解析接口
            acceptList: '.png,.jpeg,.jpg',
            fileList: [],
            docTypes: ['doc', 'docx'],

            commentForm: {
                commentType: '1',
                content: '',
                title: '',
                rank: '',
                hotNum: '',
                rankType: '',
                file: [],
                relatedCommentType: '',
            },
            rules: {
                instructionId: [
                    { required: true, message: '请选择指令', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '请输入内容', trigger: 'change' }
                ],
                title: [
                    // { required: true, message: '请输入标头', trigger: 'change' }
                ],
                rank: [
                    { required: true, message: '请输入热搜排名', trigger: 'change' }
                ],
                hotNum: [
                    { required: false, message: '请输入热度值', trigger: 'change' }
                ],
                rankType: [
                    { required: false, message: '请选择文章类型', trigger: 'change' }
                ],
            },
            instructionList: [],

            imageTypes: ['png', 'jpeg', 'jpg'],
            videoTypes: ['mp4', 'mkv'],
            nowTeamType: 1, // 当前队伍
            //图片视频上传数量限制
            imageLimit: 9,
            videoLimit: 1,
            submitLoading: false, // 发布按钮loading状态


            postsList: [], //关联贴文列表
            AILoading: false,
            AIsessionId: null, // AI会话id
            AITagList: [
                { tagName: 'AI标签1AI标签1AI标签1AI标签1AI标签1AI标签1AI标签1AI标签1' },
                { tagName: 'AI标签2' },
                { tagName: 'AI标签3' },
            ],
            AITagText: '',
        }
    },
    computed: {
        templateList() {
            if (this.detailInfo.teamName == '蓝队') {
                return [
                    { label: '热搜榜', value: '1' },
                    // { label: '热议话题', value: '2' },
                    { label: '爆料贴文', value: '2' },
                    // { label: '普通评论', value: '3' },
                    { label: '衍生话题', value: '10' },
                ]
            } else if (this.detailInfo.teamName == '红队') {
                return [
                    { label: '爆料贴文', value: '2' },
                    { label: '情况通报', value: '4' },
                    // { label: '警情通报', value: '5' },
                    { label: '分析报告', value: '5' },
                    // { label: '普通评论', value: '3' },
                    { label: '文件函', value: '9' },
                    { label: '衍生话题', value: '10' },
                ]
            } else {
                return [
                    // { label: '普通评论', value: '3' }
                ]
            }
        },
        submitDisable() { // 发布按钮的disable
            // return (!this.currentStep || !this.nextStep) || this.selfSeconds <= 0
            return (!this.currentStep) || this.selfSeconds <= 0 || this.submitLoading
        },
        selfSeconds() { // 当前队伍的倒计时秒数
            if (this.nowTeamType == 1) {
                return this.$parent.$parent.$refs['miniVsScreen'].redSeconds
            } else {
                return this.$parent.$parent.$refs['miniVsScreen'].blueSeconds
            }
        },
    },
    watch: {
        templateList: {
            immediate: true,
            handler(newVal) {
                if (newVal.length > 0) {
                    this.commentForm.commentType = newVal[0]?.value;
                }
            }
        },
        detailInfo: {
            immediate: true,
            deep: true,
            handler(newVal) {
                this.getDist()
                this.nowTeamType = (newVal.teamName == '红队') ? 1 : 2
            },
        },
    },
    created() {
        // 安全监听消息
        this.unlistenMessage = safeOn(WS_EVENTS.MESSAGE, this.handleMessage)
    },
    mounted() {
        // this.getDist()
    },
    beforeDestroy() {
        // 清理所有监听
        this.unlistenMessage?.()
    },
    methods: {
        getDist() {
            if (!this.detailInfo.drillTaskId) {
                return
            }
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,
                // processStageId: this.currentStep,
                commentType: "6", //评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令
                roleInfo: this.detailInfo.roleInfo,//角色类型
            }
            drillCommentQueryApi(params).then(res => {
                this.instructionList = res.data.map(item => {
                    return {
                        label: item.content,
                        value: item.commentId,
                        commentOrder: item.commentOrder,
                    }
                })
            })
        },
        // 获取关联贴文
        getPostList() {
            if (!this.detailInfo.drillTaskId) {
                return
            }
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,
                commentType: "2",
                roleInfo: this.detailInfo.roleInfo,//角色类型
            }
            drillCommentQueryApi(params).then(res => {
                this.postsList = res.data.map(item => {
                    return {
                        label: item.content,
                        value: item.commentId,
                    }
                })
            })
        },

        handleMessage({ channel, data }) {
            if (channel === 'DRILL_COMMENT') { //评论通知
                let { drillTaskId, commentId, content, teamType, commentType, commentOrder } = data;
                if ((commentType != '6')) { //评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令
                    // 非指令
                    return
                }
                if (drillTaskId != this.detailInfo.drillTaskId) {
                    // drillTaskId不一致
                    return
                }
                if ((teamType != this.nowTeamType)) {
                    // 非我方指令
                    return
                }
                this.instructionList.unshift({
                    label: content,
                    value: commentId,
                    commentOrder: commentOrder,
                })
            }
            if (channel === 'DRILL_STAGE') { // 阶段通知

            }
        },
        // 重置表单
        resetForm() {
            this.commentForm.content = ''
            this.commentForm.rank = undefined
            this.commentForm.hotNum = undefined
            this.commentForm.rankType = ''
            this.commentForm.relatedCommentType = ''
            this.commentForm.title = ''
            this.fileList = []
            this.$nextTick(() => {
                this.$refs['form'].clearValidate()
            })
        },
        // 发布
        submitComment() {
            // 防止重复提交
            if (this.submitLoading) {
                return
            }

            this.$refs.form.validate(valid => {
                if (valid) {
                    // 开始loading
                    this.submitLoading = true

                    let params = {
                        drillTaskId: this.detailInfo.drillTaskId,//演练任务ID
                        processStageId: this.currentStep,//所属阶段ID
                        commentType: this.commentForm.commentType,//评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令
                        roleInfo: this.detailInfo.roleInfo,//角色类型
                        isCaptain: (this.detailInfo.roleInfo == 'redCap' || this.detailInfo.roleInfo == 'blueCap'),//是否队长发布（0:否 1:是）
                        content: this.commentForm.content,//评论内容

                        parentCommentId: this.commentForm.instructionId,//指令ID
                    }
                    if (this.commentForm.commentType == '1') { // 热搜榜才有排名
                        params.rank = this.commentForm.rank
                        params.hotNum = this.commentForm.hotNum
                        params.rankType = this.commentForm.rankType
                    }
                    if (this.commentForm.commentType == '2') { // 爆料贴文才有关联内容
                        params.relatedCommentType = this.commentForm.relatedCommentType
                    }
                    if (this.commentForm.commentType == '3' || this.commentForm.commentType == '2') { // 普通评论才能上传附件
                        params.file = this.fileList.map(item => item.fileId) || []
                    }
                    if (this.commentForm.commentType == '9') { // 请示函才有标头
                        params.title = this.commentForm.title
                    }

                    drillCommentPublishApi(params).then(res => {
                        this.$message.success('发布成功')
                        this.resetForm()
                    }).catch(error => {
                        console.error('发布失败:', error)
                        this.$message.error('发布失败，请重试')
                    }).finally(() => {
                        // 结束loading
                        this.submitLoading = false
                    })
                } else {
                    // 表单验证失败时也要确保loading状态正确
                    this.submitLoading = false
                }
            })
        },
        uploadImg() {
            this.acceptList = this.imageTypes.map(item => '.' + item).join(',')
        },
        uploadVideo() {
            this.acceptList = this.videoTypes.map(item => '.' + item).join(',')
        },
        // 上传前校检格式和大小
        handleBeforeUpload(file) {
            const isLt2M = file.size / 1024 / 1024
            if (isLt2M > 30) {
                this.$message.error('上传文件大小不能超过30MB')
                return false; // 阻止文件上传
            }

            // 动态获取允许的扩展名列表
            const allowedExtensions = this.acceptList
                .split(',')
                .map(ext => ext.trim().toLowerCase().replace('.', '')) // 转换为小写并去除点号
                .filter(ext => ext) // 过滤空值

            // 获取文件扩展名
            const fileExtension = file.name
                .split('.')
                .pop()
                .toLowerCase()

            // 验证文件类型
            const isValidType = allowedExtensions.includes(fileExtension)

            if (!isValidType) {
                const allowedTypes = allowedExtensions.join(', ')
                this.$message.error(`只允许上传 ${allowedTypes} 格式的文件`)
                return false
            }


            // 数量限制校验
            const counts = this.countExistingFiles()
            const newFileType = this.getFileType(file)
            // 图片数量校验
            if (newFileType === 'image' && counts.image >= this.imageLimit) {
                this.$message.error(`最多上传${this.imageLimit}张图片`)
                return false
            }

            // 视频数量校验
            if (newFileType === 'video' && counts.video >= this.videoLimit) {
                this.$message.error(`最多上传${this.videoLimit}个视频`)
                return false
            }


            return true
        },
        // 文件个数超出
        handleExceed() {
        },
        // 文件上传成功处理
        async handleFileSuccess(response, file, fileList) {
            fileList[fileList.length - 1] = { ...file, fileId: response.data[0] }
            this.fileList = fileList
        },
        handleFileRemove(file, fileList) {
            this.fileList = fileList
        },

        // 获取当前文件类型
        getFileType(file) {
            const isImage = this.imageTypes.some(ext => file.name.toLowerCase().endsWith(ext))
            return isImage ? 'image' : 'video'
        },

        // 统计现有文件
        countExistingFiles() {
            return this.fileList.reduce((acc, file) => {
                const type = this.getFileType(file)
                acc[type] = (acc[type] || 0) + 1
                return acc
            }, { image: 0, video: 0 })
        },

        // 处理Word文档上传前的验证
        handleDocBeforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过10MB')
                return false
            }

            // 验证文件类型
            const fileName = file.name
            const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()
            if (!this.docTypes.includes(fileExtension)) {
                this.$message.error('只允许上传 .doc/.docx 格式的文件')
                return false
            }

            return true
        },

        // 文档上传数量超出限制
        handleDocExceed() {
            this.$message.error('最多只能上传1个Word文档')
        },

        // 处理文档上传成功
        handleDocSuccess(response, file) {
            this.$message.success('正在解析文档...')
            if (response.code === 200) {
                // 将解析结果显示在文本框中
                this.commentForm.content = response.data.content || response.data
                this.$message.success('文档解析成功')
            } else {
                this.$message.error(response.msg || '文档解析失败')
            }
        },

        async handleMessage({ channel, data }) {
            if (channel === 'AI_STREAM_CHUNK') { // AI生成衍生话题
                let { drillTaskId, sessionId } = data
                if (drillTaskId != this.drillTaskId) return
                if (sessionId != this.AIsessionId) return
                this.AITagText += data.content
                if(data.isEnd){
                    this.AITagList = this.AITagText.split('，').map(item => {
                        return { tagName: item }
                    })
                    this.AILoading = false
                }
            }


        },
        // AI一键生成
        getAIRelatedTopics() {
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,//演练任务ID
                commentId: this.commentForm.hotTopicCommentId,//热议话题ID
            }
            this.AILoading = true
            AIRelatedTopicsApi(params).then(res => {
                // this.commentForm.content = res.data
                if (res.code == 200) {
                    this.AITagList = []
                    this.AIsessionId = res.data
                    this.$message.success('开始生成')
                } else {
                    this.AILoading = false
                }
            }).catch(() => {
                this.AILoading = false
            })
        },
    }
}
</script>
<style scoped lang="scss">
.publicComment {

    .tampTab {
        .tabTitle {
            font-weight: 600;
        }
    }

    .upload-btn {
        margin-top: -15px;
        display: flex;
        color: #999999;
        font-size: 14px;

        .upload-btn-item {
            display: flex;
            align-items: center;
            margin-right: 20px;
            cursor: pointer;

            img {
                height: 14px;
                margin-right: 5px;
                margin-top: 1px;
            }
        }
    }

    .templatePreview {
        padding: 20px;
        background-color: #D8D8D8;

        .previewTitle {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 20px;
        }

        img {
            width: 100%;
        }
    }

    .formLabel {
        width: 70px;
        display: inline-block;

        .refresh-btn {
            color: #999999;
            cursor: pointer;
        }
    }

    .AI-tags {
        border: 1px solid #DCDFE6;
        border-radius: 4px;
        min-height: 96px;
        padding: 5px 15px;

        .AI-tags-item {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .AI-tag {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                /* 允许flex项目收缩到内容宽度以下 */
                color: #EB7350;

                >span {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .publish-btn {
                margin-left: 20px;
                display: flex;
                align-items: center;
                gap: 5px;
                color: #247CFF;
                line-height: normal;
                cursor: pointer;
            }
        }
    }
}

.tabMain {
    display: flex;
    background-color: #E5F5FF;
    color: #666666;
    border-radius: 10px;
    margin: 10px 0;

    >div {
        flex: 1;
        text-align: center;
        padding: 20px 0;
        cursor: pointer;
    }

    .activeTab {
        background-color: #247CFF;
        color: #FFFFFF;
        border-radius: 10px;
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .publicComment {

        /* 表单元素优化 */
        ::v-deep .el-form-item {
            margin-bottom: 10px;
        }

        ::v-deep .el-select {
            width: 100%;

            .el-input__inner {
                height: 30px;
                line-height: 30px;
                font-size: 12px;
            }
        }

        ::v-deep .el-textarea__inner {
            font-size: 12px;
            padding: 5px;
        }

        ::v-deep .el-input-number {
            width: 100px;
            line-height: 28px;

            .el-input__inner {
                height: 30px;
                line-height: 30px;
                font-size: 12px;
            }

            .el-input-number__decrease,
            .el-input-number__increase {
                width: 25px;
                height: 15px;
                line-height: 15px;
            }
        }

        .tampTab {
            .tabTitle {
                font-size: 12px;
                font-weight: 600;
            }
        }

        .upload-btn {
            margin-top: -10px;
            font-size: 12px;

            .upload-btn-item {
                margin-right: 10px;

                img {
                    height: 12px;
                    margin-right: 3px;
                }
            }
        }

        .templatePreview {
            padding: 10px;
            margin-top: 10px;

            .previewTitle {
                font-size: 12px;
                margin-bottom: 10px;
            }
        }

        .formLabel {
            width: 60px;
            font-size: 12px;
        }

        /* 按钮优化 */
        ::v-deep .el-button {
            padding: 5px 10px;
            font-size: 12px;
        }
    }

    .tabMain {
        margin: 5px 0;

        >div {
            padding: 10px 0;
            font-size: 12px;
        }
    }
}
</style>
