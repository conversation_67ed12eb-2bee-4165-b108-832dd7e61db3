<template>
    <div class="vsScreen">
        <!-- Loading 遮罩层 -->
        <div v-if="loading" class="vs-screen-loading">
            <div class="loading-content">
                <i class="el-icon-loading loading-icon"></i>
                <div class="loading-text">正在加载演练数据...</div>
            </div>
        </div>

        <!-- <button style="font-size: 16em;" @click="sendmsg">1111</button> -->
        <!-- 布局切换按钮 -->
        <div class="layout-toggle" @click="toggleLayout">
            <span :class="{'active': isDoubleColumn}">双屏</span>
            <span :class="{'active': !isDoubleColumn}">单屏</span>
        </div>
        <!-- 弹幕开关 -->
        <div class="danmaku-toggle" @click="toggleDanmaku">
            <img :src="isDanmakuEnabled ? require('@/assets/images/simulatedVS/danmaku-open.png') : require('@/assets/images/simulatedVS/danmaku-close.png')"
                alt="弹幕开关" />
        </div>
        <div class="qrcode-block" @click="amplifyQrcode">
            <qrcode-vue class="qrcode" :value="qrcodeUrl" :size="130" level="H" />
            <div v-show="!isMobile()" class="qrcode-tips">请扫码签到</div>
        </div>
        <div class="top-title">
            <!-- 网络舆情引导实训实战系统 -->
            <div class="titleMain" ref="titleMain" :class="{ 'title-scrolling': titleNeedsScroll }">
                <span ref="titleText" class="title-text">{{ detailInfo.taskTitle }}</span>
            </div>
        </div>
        <div v-show="false" class="top-notice">
            <div class="scroll-text" :style="{animationDuration: `${textDuration}s`,}">
                {{detailInfo.drillEvent}}
            </div>
        </div>

        <div v-show='localStageType==1' class="countdown-block">
            <div class="countdown-item">
                <countdown v-model="blueSeconds" @countdown-finished="onCountdownEnd" style="font-size: 1em;" />
            </div>
            <div v-show="showStageScore" class="top-score">
                <div class="top-score-item blueScore">
                    <div class="top-score-title">得分：</div>
                    <div class="top-score-value">
                        {{`${scoreInfo.blueTeamScore&&scoreInfo.blueTeamScore.basicScore.scoreString?scoreInfo.blueTeamScore.basicScore.scoreString:0}`}}
                    </div>
                </div>
                <div class="top-score-item redScore">
                    <div class="top-score-title">得分：</div>
                    <div class="top-score-value">
                        {{`${scoreInfo.redTeamScore&&scoreInfo.redTeamScore.basicScore.scoreString?scoreInfo.redTeamScore.basicScore.scoreString:0}`}}
                    </div>
                </div>
            </div>
            <div v-show="!showStageScore">
            </div>
            <div class="countdown-item">
                <countdown v-model="redSeconds" @countdown-finished="onCountdownEnd" style="font-size: 1em;"
                    themeColor="red" />
            </div>
        </div>
        <div v-show='localStageType==1' class="statistics-block">
            <div class="statistics-block-item">
                <span class="statistics-title">评论数：</span>
                <span class="statistics-num" style="color: #0543C6;">
                    {{
                    `${statisticsInfo.blueTeamStatistics&&statisticsInfo.blueTeamStatistics.totalReplyCount?statisticsInfo.blueTeamStatistics.totalReplyCount:'0'}`
                    }}
                </span>
                <span class="statistics-title">点赞数：</span>
                <span class="statistics-num" style="color: #0543C6;">
                    {{
                    `${statisticsInfo.blueTeamStatistics&&statisticsInfo.blueTeamStatistics.totalLikeCount?statisticsInfo.blueTeamStatistics.totalLikeCount:'0'}`
                    }}
                </span>
            </div>
            <div class="statistics-block-item">
                <span class="statistics-title">评论数：</span>
                <span class="statistics-num" style="color: #D41503;">
                    {{
                    `${statisticsInfo.redTeamStatistics&&statisticsInfo.redTeamStatistics.totalReplyCount?statisticsInfo.redTeamStatistics.totalReplyCount:'0'}`
                    }}
                </span>
                <span class="statistics-title">点赞数：</span>
                <span class="statistics-num" style="color: #D41503;">
                    {{
                    `${statisticsInfo.redTeamStatistics&&statisticsInfo.redTeamStatistics.totalLikeCount?statisticsInfo.redTeamStatistics.totalLikeCount:'0'}`
                    }}
                </span>
            </div>
        </div>


        <div v-show='localStageType==2' class="score-block">
            <div class="score-item">
                <div class="score-item-title">
                    蓝方得分
                </div>
                <div class="score-item-value blueScore">
                    {{
                    scoreInfo.blueTeamScore&&scoreInfo.blueTeamScore.totalScoreString?scoreInfo.blueTeamScore.totalScoreString:0
                    }}
                </div>
            </div>
            <div class="centerLogo">
                双方总结
                <img src="@/assets/images/simulatedVS/VS-logo.svg" alt="" />
            </div>
            <div class="score-item">
                <div class="score-item-title">
                    红方得分
                </div>
                <div class="score-item-value redScore">
                    {{
                    scoreInfo.redTeamScore&&scoreInfo.redTeamScore.totalScoreString?scoreInfo.redTeamScore.totalScoreString:0
                    }}
                </div>
            </div>
        </div>

        <!-- 弹幕容器区域 - 只覆盖上方区域 -->
        <div v-show="isDanmakuEnabled" class="danmaku-container">
            <vueDanmaku ref="barrage" :speeds="100" :channels="5" :performanceMode="true" :randomChannel="true"
                style="font-size: 1em;" />
        </div>

        <div class="scroll-block" ref="scrollContainer">
            <div v-show='localStageType==1' :class="['whatHappened-block', {'single-column': !isDoubleColumn}]">
                <TimelineItem v-for="(item, index) in sortedOptions" :key="`commentId_${item.commentId}`" :item="item"
                    :isMarginTop="index>0 && item.teamType != sortedOptions[index - 1].teamType">
                    <!-- 指令 -->
                    <div v-if="item.commentType=='6'" class="timeline-content">
                        <InstructionTemplate :commentOrder="item.commentOrder" :content="item.content||''" />
                    </div>
                    <!-- 评论 -->
                    <TimelineContent v-else :item="item" :showInstruction="showInstruction"
                        @amplify="(e)=>amplifyElement(item,e)">
                        <!-- 热搜榜 -->
                        <HotSearchTemplate v-if="item.commentType=='1'" :item="item" />

                        <!-- 热议话题 -->
                        <!-- <HotTopicTemplate v-if="item.commentType=='2'" :content="item.content" /> -->

                        <!-- 爆料贴文 -->
                        <PostTemplate v-if="item.commentType=='2'" :item="item" :imageTypes="imageTypes"
                            :videoTypes="videoTypes" :drillTaskId="drillTaskId" :processStageId="currentStep"
                            :initialSortBy="postSortStates[item.commentId] || 'createdTime'" @comment="handelComment"
                            @like="handelLike" @sort-change="handlePostSortChange" />

                        <!-- 情况通报 -->
                        <SituationReportTemplate v-if="item.commentType=='4'" :content="item.content||''" />

                        <!-- 警情通报 -->
                        <!-- <PoliceReportTemplate v-if="item.commentType=='5'" :content="item.content||''" /> -->

                        <!-- 分析报告 -->
                        <AnalysisReportTemplate v-if="item.commentType=='5'" :content="item.content||''" />

                        <!-- 文件函 -->
                        <LetterTemplate v-if="item.commentType=='9'" :content="item.content||''"
                            :title="item.title||''" />

                        <!-- 普通评论 -->
                        <CommentTemplate v-if="item.commentType=='3'" :avatar="item.avatar" :nickName="item.nickName"
                            :content="item.content" :fileList="item.fileList" :imageTypes="imageTypes"
                            :videoTypes="videoTypes" />

                        <!-- 专家点评 -->
                        <ExpertCommentTemplate v-if="item.commentType=='8'" :content="item.content||''" />
                    </TimelineContent>
                </TimelineItem>
            </div>

            <!-- 返回得分总结 -->
            <div v-show='localStageType==1&&newCurrentStage.stageType==2' class="checkStageTypeBtn">
                <el-button type="primary" @click="switchToScoreDetail">返回得分总结</el-button>
            </div>

            <div v-show='localStageType==2' class="scoreDetail-block">
                <div v-if="false" class="scoreDetail-item" style="border-right: 0.1em solid #e6e6e6;">
                    <div class="detail-item" v-for="scoreItem in scoreInfo.blueScore" :key="scoreItem.stageName">
                        <!-- <div class="detail-process">{{ scoreItem.stageName }}</div>
                            <div v-if="scoreItem.scoreName&&scoreItem.scoreName.length>0" class="detail-option">
                                <div class="option-item" v-for="(optionItem,optionIndex) in scoreItem.scoreName"
                                    :key="optionIndex">
                                    {{optionItem }}
                                </div>
                            </div>
                            <div v-else class="detail-option">
                                <div class="option-item">无操作</div>
                            </div> -->
                        <div v-if="scoreItem.scoreName&&scoreItem.scoreName.length>0" class="detail-process">
                            <div class="option-item" v-for="(optionItem,optionIndex) in scoreItem.scoreName"
                                :key="optionIndex">
                                {{optionItem }}
                            </div>
                        </div>
                        <div class="detail-score">{{`+${scoreItem.score}`}}</div>
                    </div>
                </div>
                <div v-if="false" class="scoreDetail-item red-scoreDetail">
                    <div class="detail-item" v-for="scoreItem in scoreInfo.redScore" :key="scoreItem.stageName">
                        <!-- <div class="detail-process">{{ scoreItem.stageName }}</div>
                            <div v-if="scoreItem.scoreName&&scoreItem.scoreName.length>0" class="detail-option">
                                <div class="option-item" v-for="(optionItem,optionIndex) in scoreItem.scoreName"
                                    :key="optionIndex">
                                    {{optionItem }}
                                </div>
                            </div>
                            <div v-else class="detail-option">
                                <div class="option-item">无操作</div>
                            </div> -->
                        <div v-if="scoreItem.scoreName&&scoreItem.scoreName.length>0" class="detail-process">
                            <div class="option-item" v-for="(optionItem,optionIndex) in scoreItem.scoreName"
                                :key="optionIndex">
                                {{optionItem }}
                            </div>
                        </div>
                        <div class="detail-score">{{`+${scoreItem.score}`}}</div>
                    </div>
                </div>


                <!-- 新数据结构的得分总结 -->
                <div class="scoreDetail-item" style="border-right: 0.1em solid #e6e6e6;">
                    <div class="detail-item">
                        <div class="detail-process">
                            <div class="option-item">
                                基础得分
                            </div>
                        </div>
                        <div class="detail-score">
                            {{`+${scoreInfo.blueTeamScore&&scoreInfo.blueTeamScore.basicScore.scoreString?scoreInfo.blueTeamScore.basicScore.scoreString:0}`}}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-process">
                            <div class="option-item">
                                专家得分
                            </div>
                        </div>
                        <div class="detail-score">
                            {{`+${scoreInfo.blueTeamScore&&scoreInfo.blueTeamScore.expertScore.scoreString?scoreInfo.blueTeamScore.expertScore.scoreString:0}`}}
                        </div>
                    </div>
                </div>
                <div class="scoreDetail-item red-scoreDetail">
                    <div class="detail-item">
                        <div class="detail-process">
                            <div class="option-item">
                                基础得分
                            </div>
                        </div>
                        <div class="detail-score">
                            {{`+${scoreInfo.redTeamScore&&scoreInfo.redTeamScore.basicScore.scoreString?scoreInfo.redTeamScore.basicScore.scoreString:0}`}}
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-process">
                            <div class="option-item">
                                专家得分
                            </div>
                        </div>
                        <div class="detail-score">
                            {{`+${scoreInfo.redTeamScore&&scoreInfo.redTeamScore.expertScore.scoreString?scoreInfo.redTeamScore.expertScore.scoreString:0}`}}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 演练明细按钮 -->
            <div v-show='localStageType==2' class="checkStageTypeBtn">
                <el-button type="primary" @click="switchToTimeline">演练明细</el-button>
            </div>
        </div>

        <!-- 评论弹窗 -->
        <comment-dialog :visible.sync="commentDialog" :form-data.sync="commentForm"
            :upload-file-list.sync="uploadFileList" :drill-task-id="drillTaskId" @submit="handleCommentSubmit" />

        <!-- 放大内容弹窗 -->
        <el-dialog title="详情查看" :visible.sync="amplifyDialog" width="80%" append-to-body close-on-click-modal
            @closed="handleAmplifyDialogClosed" class="amplify-dialog">
            <div class="amplify-content">
                <!-- 爆料贴文 -->
                <PostTemplate v-if="amplifyItem && amplifyItem.commentType=='2'"
                    :key="`amplify-${amplifyItem.commentId}-${amplifyUpdateKey}`"
                    :item="latestAmplifyItem || amplifyItem" :imageTypes="imageTypes" :videoTypes="videoTypes"
                    :drillTaskId="drillTaskId" :processStageId="currentStep" :initialSortBy="amplifyCurrentSortBy"
                    @comment="handelComment" @like="handelLike" @sort-change="handleAmplifyPostSortChange" />
                <!-- 其他内容类型 -->
                <div v-else v-html="amplifyContent"></div>
            </div>
        </el-dialog>

        <!-- 二维码放大弹窗 -->
        <div class="qrcode-overlay" v-if="qrcodeEnlarged" @click="amplifyQrcode">
            <div class="qrcode-enlarged">
                <qrcode-vue :value="qrcodeUrl" :size="300" level="H" />
                <div class="qrcode-tips">请扫码签到</div>
                <div class="qrcode-close-hint">点击任意位置关闭</div>
            </div>
        </div>
    </div>
</template>

<script>
import countdown from '@/views/simulatedVS/components/countdown.vue'
import { WS_EVENTS, safeOn } from '@/utils/eventBus'
import { drillProcessLeaveApi, commentLikeAddApi, commentLikeCancelApi, commentReplyAddApi, getTaskStagesStatisticsApi, getTaskScoresStatisticsApi } from "@/api/simulatedVS/index.js";
import QrcodeVue from "qrcode.vue";
import { baseRoute } from '@/utils/index.js'
import { getToken } from "@/utils/auth";
import { processItemFiles, processBatchFiles } from "@/utils/simulatedVS.js";

// import Barrage from '@/views/simulatedVS/components/Barrage.vue'
import vueDanmaku from '@/views/simulatedVS/components/vueDanmaku.vue'
import CommentDialog from '@/views/simulatedVS/components/commentDialog.vue'

// 导入模板组件
import HotSearchTemplate from '@/views/simulatedVS/template/HotSearchTemplate.vue'
import PostTemplate from '@/views/simulatedVS/template/PostTemplate.vue'
import SituationReportTemplate from '@/views/simulatedVS/template/SituationReportTemplate.vue'
import AnalysisReportTemplate from '@/views/simulatedVS/template/AnalysisReportTemplate.vue'
import CommentTemplate from '@/views/simulatedVS/template/CommentTemplate.vue'
import ExpertCommentTemplate from '@/views/simulatedVS/template/ExpertCommentTemplate.vue'
import InstructionTemplate from '@/views/simulatedVS/template/InstructionTemplate.vue'
import HotTopicTemplate from '@/views/simulatedVS/template/HotTopicTemplate.vue'
import PoliceReportTemplate from '@/views/simulatedVS/template/PoliceReportTemplate.vue'
import LetterTemplate from '@/views/simulatedVS/template/LetterTemplate.vue'
import TimelineContent from '@/views/simulatedVS/template/TimelineContent.vue'
import TimelineItem from '@/views/simulatedVS/template/TimelineItem.vue'

export default {
    components: {
        countdown,
        QrcodeVue,
        // Barrage,
        CommentDialog,
        vueDanmaku,
        HotSearchTemplate,
        PostTemplate,
        SituationReportTemplate,
        AnalysisReportTemplate,
        CommentTemplate,
        ExpertCommentTemplate,
        InstructionTemplate,
        HotTopicTemplate,
        PoliceReportTemplate,
        LetterTemplate,
        TimelineContent,
        TimelineItem
    },
    props: {
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        newCurrentStage: { // 当前阶段信息
            type: Object,
            default() {
                return {
                    blueStageScore: '',
                    redStageScore: '',
                    scoreType: '',
                }
            }
        },
        detailInfo: {
            type: Object,
            default() {
                return {
                    drillEvent: ''
                }
            }
        },
        showInstruction: {
            type: Boolean,
            default: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            drillTaskId: (this.$route.query && this.$route.query.drillTaskId) || '',
            blueSeconds: 0,
            redSeconds: 0,
            originData: [],// 备份的原始数据
            imageTypes: ['png', 'jpeg', 'jpg'],
            videoTypes: ['mp4', 'mkv'],

            scoreInfo: {
                // blueScore: [],
                // blueTotalScore: '0',
                // redScore: [],
                // redTotalScore: '0',

                "redTeamScore": {
                    "basicScore": {
                        "scoreString": "0.0"
                    },
                    "expertScore": {
                        "scoreString": "0.0"
                    },
                    "totalScoreString": "0.0"
                },
                "blueTeamScore": {
                    "basicScore": {
                        "scoreString": "0.0"
                    },
                    "expertScore": {
                        "scoreString": "0.0"
                    },
                    "totalScoreString": "0.0"
                },
            },
            qrcodeUrl: 'https://www.baidu.com',

            textDuration: 10, // 基准动画时长

            scrollIndex: 0, // 滚动计数器


            // 分页数据
            pagination: {
                page: 1,
                pageSize: 10,
                total: 0,
                loading: false,
                hasMore: true
            },
            // 当前渲染的数据
            visibleOptions: [],


            commentDialog: false,
            commentForm: { content: '' },
            uploadFileList: [],
            nowCommentInfo: {},

            // 放大内容相关
            amplifyDialog: false,
            amplifyContent: '',
            amplifyItem: null, // 当前放大的项目
            amplifyUpdateKey: 0, // 用于强制更新放大弹窗中的PostTemplate组件
            amplifyCurrentSortBy: 'createdTime', // 放大弹窗中的排序状态

            // 二维码放大相关
            qrcodeEnlarged: false,

            // 布局模式：true为双列，false为单列
            isDoubleColumn: !this.isMobile(),

            // 弹幕开关：true为开启，false为关闭
            isDanmakuEnabled: true,

            // 数据初始化完成标志
            dataInitialized: false,

            // 数据初始化进行中标志，防止重复初始化
            dataInitializing: false,

            // 爆料贴文排序状态管理
            postSortStates: {}, // 存储每个爆料贴文的排序状态，key为commentId

            // 标题滚动相关
            titleNeedsScroll: false, // 标题是否需要滚动
            titleScrollTimer: null, // 标题滚动检测定时器

            statisticsInfo: {}, // 阶段点赞评论统计信息

            // 本地控制的阶段类型，用于在得分总结页面和演练时间线页面之间切换
            localStageType: 1, // 1: 演练时间线页面, 2: 得分总结页面

            // 滚动到评论的状态标志
            isScrollingToComment: false,
        }
    },
    // 添加计算属性和样式配置
    computed: {
        sortedOptions() {
            // return [...this.visibleOptions].sort((a, b) =>
            //     new Date(a.time) - new Date(b.time)
            // )
            return this.visibleOptions
        },
        userId() {
            return this.$store.state.user.userId
        },
        showStageScore() {
            //scoreType 阶段得分 1:不显示得分 2:显示得分
            // 添加数据初始化完成的判断，避免页面刷新时显示默认值
            return this.dataInitialized &&
                this.newCurrentStage.scoreType == 2 &&
                !this.blueSeconds &&
                !this.redSeconds &&
                (this.scoreInfo.blueTeamScore.basicScore.scoreString !== '' || this.scoreInfo.redTeamScore.basicScore.scoreString !== '')
        },
        // 获取放大项目的最新数据（计算属性，避免重复调用）
        latestAmplifyItem() {
            if (!this.amplifyItem) return null;

            // 从visibleOptions中查找最新数据
            const latestData = this.visibleOptions.find(
                item => item.commentId === this.amplifyItem.commentId
            );

            if (latestData) {
                return latestData;
            }

            // 如果在visibleOptions中找不到，从originData中查找
            const originData = this.originData.find(
                item => item.commentId === this.amplifyItem.commentId
            );

            if (originData) {
                return originData;
            }

            // 如果都找不到，返回原始数据
            return this.amplifyItem;
        }
    },
    watch: {
        // detailInfo变化则全面刷新
        detailInfo: {
            immediate: true,
            deep: true,
            handler() {
                // 重置所有初始化相关标志
                this.dataInitialized = false;
                this.dataInitializing = false;
                this.messageInit()
                // this.countdownInit() // 后端逻辑不完善，不如直接监听ws倒计时
            },
        },
        '$route.query.drillTaskId'(newVal) {
            if (newVal) {
                this.drillTaskId = newVal
            }
        },
        'detailInfo.drillEvent'() {
            this.calcScrollSpeed()
        },
        'detailInfo.taskTitle'() {
            this.checkTitleScroll()
        },
        // 监听newCurrentStage.stageType变化，同步到localStageType
        'newCurrentStage.stageType': {
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    this.localStageType = newVal
                }
            }
        }
    },
    created() {
        // 安全监听消息
        this.unlistenMessage = safeOn(WS_EVENTS.MESSAGE, this.handleMessage)

        // 监听连接状态变化
        this.unlistenConnect = safeOn(WS_EVENTS.CONNECT, () => {
            console.log('WebSocket connected')
        })
        this.unlistenDisconnect = safeOn(WS_EVENTS.DISCONNECT, () => {
            console.log('WebSocket disconnected')
        })

        // 监听滚动到评论事件
        this.unlistenScrollToComment = () => {
            this.$root.$off('scroll-to-comment', this.handleScrollToComment);
        };
        this.$root.$on('scroll-to-comment', this.handleScrollToComment);

        this.getStagesStatistics()
    },
    // 在组件中增加容器尺寸监听
    mounted() {
        // 打印当前地址栏地址，拼接/vsSingUp的地址
        let url = document.location.pathname;
        let index = baseRoute(url)
        this.qrcodeUrl = `${document.location.origin}/${index}/vsSingUp?drillTaskId=${this.drillTaskId}`

        this.$nextTick(() => {
            const container = this.$el
            this.resizeObserver = new ResizeObserver(entries => {
                for (let entry of entries) {
                    const width = entry.contentRect.width
                    container.style.fontSize = width / 1300 + 'px' // 根据容器宽度设置基准单位，992px以下使用900px的基准单位

                    // 根据屏幕宽度自动切换基准单位
                    if (width < 992 || this.isMobile()) {
                        container.style.fontSize = width / 900 + 'px'
                    }
                }
            })
            this.resizeObserver.observe(container)
        })

        // 添加窗口大小变化监听
        window.addEventListener('resize', this.handleResize);

        // 添加滚动监听方法
        const scrollContainer = this.$el.querySelector('.scroll-block')
        scrollContainer.addEventListener('scroll', this.handleScroll)

        // 初始化标题滚动检测
        this.checkTitleScroll()
    },
    beforeDestroy() {
        this.leavePage() // 中途离开
        this.resizeObserver.disconnect()

        // 清理所有监听
        this.unlistenMessage && this.unlistenMessage()
        this.unlistenConnect && this.unlistenConnect()
        this.unlistenDisconnect && this.unlistenDisconnect()
        this.unlistenScrollToComment && this.unlistenScrollToComment()

        // 移除窗口大小变化监听
        // window.removeEventListener('resize', this.handleResize);

        // 移除滚动监听方法
        const scrollContainer = this.$el.querySelector('.scroll-block')
        if (scrollContainer) {
            scrollContainer.removeEventListener('scroll', this.handleScroll)
        }

        // 清理标题滚动定时器
        if (this.titleScrollTimer) {
            clearTimeout(this.titleScrollTimer)
        }
    },
    methods: {
        // 检测是否为移动设备
        isMobile() {
            // 检查是否为移动设备
            const userAgent = navigator.userAgent.toLowerCase();
            return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile|tablet/i.test(userAgent);
        },

        // 处理窗口大小变化
        handleResize() {
            // 检测窗口宽度，小于992px或者是移动设备时使用单屏布局
            if (window.innerWidth < 992 || this.isMobile()) {
                this.isDoubleColumn = false;
            }
            // 窗口大小变化时重新检测标题滚动
            this.checkTitleScroll();
        },

        // 检测标题是否需要滚动
        checkTitleScroll() {
            // 清除之前的定时器
            if (this.titleScrollTimer) {
                clearTimeout(this.titleScrollTimer);
            }

            // 延迟检测，确保DOM已更新
            this.titleScrollTimer = setTimeout(() => {
                this.$nextTick(() => {
                    const titleContainer = this.$refs.titleMain;
                    const titleText = this.$refs.titleText;

                    if (!titleContainer || !titleText) {
                        this.titleNeedsScroll = false;
                        return;
                    }

                    // 获取容器和文本的实际宽度
                    const containerWidth = titleContainer.offsetWidth;
                    const textWidth = titleText.scrollWidth;

                    // 判断是否需要滚动（文本宽度超过容器宽度）
                    this.titleNeedsScroll = textWidth > containerWidth;
                });
            }, 100);
        },

        calcScrollSpeed() {
            this.$nextTick(() => {
                const container = this.$el.querySelector('.top-notice');
                const textElement = this.$el.querySelector('.scroll-text');

                if (!container || !textElement) return;

                // 计算文本实际宽度
                const textWidth = textElement.scrollWidth;
                // 容器可视宽度
                const containerWidth = container.offsetWidth;

                // 动态计算动画时长（速度 = 总移动距离 / 时间）
                this.textDuration = Math.round((textWidth + containerWidth) * 10 / containerWidth);
            });
        },

        // 切换到演练时间线页面
        switchToTimeline() {
            this.localStageType = 1;
            // 切换后滚动到底部，显示最新内容
            this.$nextTick(() => {
                const container = this.$el.querySelector('.scroll-block');
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },
        // 切换到得分总结页面
        switchToScoreDetail() {
            this.localStageType = 2;
        },

        // 倒计时结束
        onCountdownEnd() {
            // console.log('倒计时结束');
            // 倒计时结束更新分数和数量统计（保底机制）
            setTimeout(() => {
                this.getStagesStatistics()
                this.getScoreInfo()
            }, 1000);
        },

        // 获取阶段点赞评论统计信息
        getStagesStatistics() {
            getTaskStagesStatisticsApi({
                drillTaskId: this.drillTaskId,
            }).then(res => {
                this.statisticsInfo = res.data || {}
            })
        },

        async handleMessage({ channel, data }) {
            // 只对非DRILL_TIME消息打印详细日志，避免控制台刷屏
            if (channel !== 'DRILL_TIME') {
                // console.log('频道:', channel)
                // console.log('内容:', data)
            }

            if (data.drillTaskId != this.drillTaskId) {
                // console.log('drillTaskId不一致')
                return
            }


            if (channel === 'DRILL_COMMENT') { // 评论通知
                let { drillTaskId, processStageId, commentType, file, commentId } = data
                // if (processStageId != this.currentStep) {
                //     return console.log('processStageId不一致')
                // }

                // showInstruction为false 不显示指令
                if (!this.showInstruction && commentType == '6') {
                    return console.log('不显示指令')
                }

                const newItem = { ...data, fileProcessed: false }
                this.visibleOptions.push(newItem)

                // 如果有文件，则处理文件
                if ((commentType == '3' || commentType == '2') && file && file.length > 0) {
                    await processItemFiles(newItem, this.videoTypes) // 使用工具函数处理文件
                }

                this.goPageButton()
            }
            if (channel === 'DRILL_TIME') { // 倒计时通知
                let { drillTaskId, processStageId, remainTimerDuration, teamType, timerType, timerDuration } = data

                if (processStageId != this.currentStep) {
                    // 只在开发环境或需要调试时打印
                    // console.log('processStageId不一致')
                    return
                }
                if (remainTimerDuration < 0) {
                    // console.log('remainTimerDuration小于0')
                    return
                }

                // 只在倒计时发生变化时打印日志，避免重复打印
                let shouldLog = false;
                if (teamType == 1) { // 1:红队 2:蓝队
                    if (this.redSeconds !== remainTimerDuration) {
                        shouldLog = true;
                        this.redSeconds = remainTimerDuration
                    }
                } else if (teamType == 2) {
                    if (this.blueSeconds !== remainTimerDuration) {
                        shouldLog = true;
                        this.blueSeconds = remainTimerDuration
                    }
                } else if (timerType == 1) {
                    if (this.redSeconds !== remainTimerDuration || this.blueSeconds !== remainTimerDuration) {
                        shouldLog = true;
                        this.redSeconds = remainTimerDuration
                        this.blueSeconds = remainTimerDuration
                    }
                }

                // 只在倒计时真正更新时打印日志
                if (shouldLog) {
                    // console.log('⏰ 倒计时更新:', {
                    //     teamType: teamType === 1 ? '红队' : teamType === 2 ? '蓝队' : '双队',
                    //     remainTimerDuration,
                    //     redSeconds: this.redSeconds,
                    //     blueSeconds: this.blueSeconds
                    // });
                }
            }
            if (channel === 'DRILL_TIME_SCORE') { // 倒计时结束后的阶段计分通知
                // let { drillTaskId, processStageId, blueStageScore, redStageScore, scoreType } = data

                // if (processStageId != this.currentStep) {
                //     return console.log('processStageId不一致')
                // }
                // console.log('倒计时结束后的阶段计分通知', blueStageScore, redStageScore)
                // this.newCurrentStage.blueStageScore = blueStageScore
                // this.newCurrentStage.redStageScore = redStageScore
                // this.newCurrentStage.scoreType = scoreType


                this.scoreInfo = data
            }

            if (channel === 'DRILL_COMMENT_REPLY') { // 回复通知
                let { drillTaskId, processStageId, replyType } = data
                this.handleCommentReply(data);
            }
            if (channel === 'DRILL_COMMENT_LIKE') { // 点赞通知
                let { drillTaskId, processStageId, commentReplyId, type, likeCount } = data

                this.handleCommentLike(data);
            }


            if (channel === 'DRILL_DANMU_SCORE') { // 弹幕通知
                let { content } = data
                let msgData = {
                    text: '',
                    color: 'rgba(255,255,255,0.4)', // 可选颜色
                    size: '20em' // 可选字号
                }
                if (content.length > 30) {
                    msgData.text = content.substring(0, 30) + '...';
                } else {
                    msgData.text = content;
                }
                if (this.isDanmakuEnabled && this.$refs.barrage) {
                    this.$refs.barrage.addMessage(msgData)
                }
            }

            if (channel === 'DRILL_TASK_STATISTICS') { // 点赞评论统计信息
                let { teamStatistics } = data
                this.statisticsInfo = teamStatistics
            }

            if (channel === 'DRILL_STAGE') { // 阶段通知
                let { drillTaskId, currentStageId, nextStageId, newCurrentStage } = data
                if (drillTaskId != this.drillTaskId) {
                    return console.log('drillTaskId不一致')
                }
                if (!nextStageId) {//到最终总结阶段，更新scoreInfo
                    this.getScoreInfo()
                }
            }
        },

        // 回复评论添加到列表中
        async handleCommentReply(replyData) {
            console.log('💬 收到WebSocket新评论通知:', {
                commentId: replyData.commentId,
                commentReplyId: replyData.commentReplyId,
                content: replyData.content
            });

            // 直接通知PostTemplate组件处理新评论（包括文件处理）
            // 使用$nextTick确保DOM更新后再发送事件
            this.$nextTick(() => {
                this.notifyPostTemplateCommentReply(replyData);
            });

            // 滚动到底部（如果需要的话）
            this.goPageButton();
        },

        // 点赞通知
        handleCommentLike(likeData) {
            // 更新visibleOptions中的数据
            this.updateLikeDataInArray(this.visibleOptions, likeData);

            // 更新originData中的数据（保持数据一致性）
            this.updateLikeDataInArray(this.originData, likeData);

            // 通知PostTemplate组件更新分页数据中的点赞数
            // 使用$nextTick确保DOM更新后再发送事件
            this.$nextTick(() => {
                this.notifyPostTemplateUpdate(likeData);
            });
        },

        // 通用方法：在指定数组中更新点赞数据
        updateLikeDataInArray(dataArray, likeData) {
            const { commentId, commentReplyId, commentType, likeCount, isLike, userId } = likeData
            if (!dataArray || dataArray.length === 0) return;

            const targetIndex = dataArray.findIndex(item => item.commentId === commentId);
            if (targetIndex === -1) return;

            const targetItem = dataArray[targetIndex];

            // 其实在当前评论与贴文分离的逻辑中，commentType == 7时，drillCommentReplyRes没有数据， 评论的更新依靠notifyPostTemplateUpdate
            if (commentType == 7) {
            } else {
                // 贴文的点赞更新
                this.$set(targetItem, 'likeCount', likeCount);
                if (userId == this.userId) {
                    this.$set(targetItem, 'isLike', isLike);
                }
            }
        },

        // 通知PostTemplate组件更新点赞数据
        notifyPostTemplateUpdate(likeData) {
            // 通过事件总线通知所有PostTemplate组件更新点赞数据
            this.$root.$emit('comment-like-update', likeData);

            // 如果当前有放大的项目且是同一个评论，更新放大项目的数据
            if (this.amplifyItem && this.amplifyItem.commentId === commentId) {
                this.updateAmplifyItemData();
            }
        },

        // 通知PostTemplate组件处理新评论（包括文件处理）
        notifyPostTemplateCommentReply(replyData) {
            // 通过事件总线通知所有PostTemplate组件处理新评论
            // PostTemplate会自行处理评论的文件，无需在此处预处理
            this.$root.$emit('comment-reply-added', {
                commentId: replyData.commentId,
                replyData: replyData
            });

            // 如果当前有放大的项目且是同一个评论，更新放大项目的数据
            if (this.amplifyItem && this.amplifyItem.commentId === replyData.commentId) {
                this.updateAmplifyItemData();
            }
        },

        // 更新放大项目数据并强制重新渲染
        updateAmplifyItemData() {
            this.amplifyUpdateKey++;
        },

        // 处理放大弹窗关闭
        handleAmplifyDialogClosed() {

            // 在关闭弹窗时，将放大弹窗的排序状态同步到原爆料贴文
            if (this.amplifyItem && this.amplifyItem.commentType === '2') {
                const currentAmplifySort = this.amplifyCurrentSortBy;
                const originalSort = this.postSortStates[this.amplifyItem.commentId] || 'createdTime';

                // 放大弹窗排序状态与原贴文不一致
                if (currentAmplifySort !== originalSort) {
                    // 更新原贴文的排序状态
                    this.$set(this.postSortStates, this.amplifyItem.commentId, currentAmplifySort);
                }
            }

            this.amplifyItem = null;
            this.amplifyUpdateKey = 0;
            this.amplifyCurrentSortBy = 'createdTime'; // 重置排序状态
        },

        // 处理爆料贴文排序状态变化（主界面）
        handlePostSortChange({ commentId, sortBy }) {
            // 更新排序状态记录
            this.$set(this.postSortStates, commentId, sortBy);
        },

        // 处理放大弹窗中的排序状态变化
        handleAmplifyPostSortChange({ commentId, sortBy }) {
            // 更新放大弹窗的排序状态
            this.amplifyCurrentSortBy = sortBy;
        },



        sendmsg() {
            console.log('sendmsg')
            let data = {
                text: '这是一条弹幕消息哈哈哈哈',
                color: 'rgba(255,255,255,0.4)', // 可选颜色
                size: '20em' // 可选字号
            }
            if (this.$refs.barrage) {
                this.$refs.barrage.addMessage(data)
            }
            // if (this.$ws) {
            //     this.$ws.send({
            //         channel: 'DRILL',
            //         data: {
            //             msg: '发现敌方坦克，请求火力支援。'
            //         }
            //     })

            // } else {
            //     console.log('未连接服务器,请稍后再试');
            // }
        },

        countdownInit() {
            // 确保有阶段数据且当前步骤有效
            if (!(this.detailInfo && this.detailInfo.drillProcessStageRes) || !this.currentStep) {
                return
            }

            // 查找匹配当前步骤的阶段数据
            const currentStage = this.detailInfo.drillProcessStageRes.find(
                stage => stage.processStageId == this.currentStep // 使用宽松相等匹配数字/字符串类型
            )

            if (currentStage) {
                // 设置红蓝方剩余时间（添加默认值保护）
                this.redSeconds = Number(currentStage.redRemainTimerDuration) || 0
                this.blueSeconds = Number(currentStage.blueRemainTimerDuration) || 0
            }
        },

        async messageInit() {
            if (!this.detailInfo.drillProcessStageRes) return

            // 防止重复初始化
            if (this.dataInitializing) {
                console.log('数据正在初始化中，跳过重复调用');
                return;
            }

            this.dataInitializing = true;

            try {
                let drillProcessStageRes = JSON.parse(JSON.stringify(this.detailInfo.drillProcessStageRes))
                if (!drillProcessStageRes || drillProcessStageRes.length == 0) {
                    this.dataInitialized = true;
                    return
                }

                let allProcessList = [] // 所有评论记录
                drillProcessStageRes.forEach(item => {
                    if (item.drillCommentResList) {
                        allProcessList = [...allProcessList, ...item.drillCommentResList]
                    }
                });

                // showInstruction为false 不显示指令
                if (!this.showInstruction) {
                    allProcessList = allProcessList.filter(item => item.commentType != '6')
                }

                // let scoreInfo = drillProcessStageRes[drillProcessStageRes.length - 1]
                // this.scoreInfo = scoreInfo

                this.getScoreInfo()


                // 保存完整数据源
                this.originData = allProcessList
                this.pagination.total = allProcessList.length

                // 初始化可见数据（标记未处理）
                const initialData = allProcessList.slice(-this.pagination.pageSize)
                    .map(item => ({ ...item, fileProcessed: false }));
                await processBatchFiles(initialData, this.videoTypes);
                this.visibleOptions = initialData

                // 初始滚动到底部
                await this.$nextTick();
                const container = this.$el.querySelector('.scroll-block');
                if (container) {
                    setTimeout(() => {
                        container.scrollTop = container.scrollHeight;
                    }, 150);
                }

                // 标记数据初始化完成
                this.dataInitialized = true;

            } catch (error) {
                console.error('初始化数据失败:', error);
                // 出错时也标记为完成，但要确保基本状态正确
                this.visibleOptions = [];
                this.originData = [];
                this.pagination.total = 0;
                this.dataInitialized = true; // 避免loading一直显示
            } finally {
                this.dataInitializing = false; // 确保标志被重置
            }
        },
        getScoreInfo() {
            getTaskScoresStatisticsApi({
                drillTaskId: this.drillTaskId,
            }).then(res => {
                if (res.code == 200) {
                    this.scoreInfo = res.data
                }
            })
        },

        // 中途离开页面
        leavePage() {
            let params = {
                drillTaskId: this.drillTaskId,
                userId: this.userId
            }
            drillProcessLeaveApi(params).then(res => {
                if (res.code == 200) {
                }
            })
        },
        // 新消息进入时，自动聚焦到页面底部
        goPageButton() {
            // if (!this.nextStep) {
            //     return
            // }
            const container = this.$el.querySelector('.scroll-block')
            if (!container) return

            // 计算是否已滚动到底部（误差控制在50px内）
            const isBottom = container.scrollHeight - container.scrollTop - container.clientHeight <= 50

            this.$nextTick(() => {
                // 只有当前在底部时自动滚动
                if (isBottom) {
                    container.scrollTo({
                        top: container.scrollHeight,
                        behavior: 'smooth' // 启用平滑滚动
                    })
                    // scrollToBottom
                }
            })
        },

        // 滚动处理逻辑（防抖+加载判断）
        handleScroll(e) {
            const container = e.target

            if (this.scrollTimer) clearTimeout(this.scrollTimer)

            this.scrollTimer = setTimeout(() => {
                const { scrollTop, scrollHeight, clientHeight } = container
                // const bottomThreshold = 150 // 适当增大阈值

                // // 添加10px容错范围
                // const isBottom = Math.abs(scrollHeight - (scrollTop + clientHeight)) <= bottomThreshold

                // if (isBottom && !this.pagination.loading && this.pagination.hasMore) {
                //     this.loadMore()
                // }
                if (scrollTop < 50 && !this.pagination.loading && this.pagination.hasMore) {
                    this.loadMore();
                }
            }, 200) // 适当缩短延迟时间
        },










        // 处理滚动到指定评论的事件
        handleScrollToComment(data) {
            const { commentId, commentReplyId } = data;

            // 防止重复调用
            if (this.isScrollingToComment) {
                console.log('⏳ 正在滚动到其他评论，忽略此次请求');
                return;
            }

            console.log('🎯 开始滚动到评论:', { commentId, commentReplyId });

            this.isScrollingToComment = true;

            // 确保在时间线页面
            if (this.localStageType !== 1) {
                this.switchToTimeline();
            }

            this.$nextTick(() => {
                this.scrollToComment(commentId, commentReplyId);
            });
        },

        // 滚动到指定评论的核心逻辑
        async scrollToComment(commentId, commentReplyId) {
            try {
                const container = this.$el.querySelector('.scroll-block');
                if (!container) {
                    console.warn('未找到滚动容器');
                    this.isScrollingToComment = false;
                    return;
                }

                // 第一步：找到对应的贴文（通过 commentId）
                const postElement = container.querySelector(`[commentId="${commentId}"]`);
                if (!postElement) {
                    console.warn(`未找到 commentId 为 ${commentId} 的贴文`);
                    this.isScrollingToComment = false;
                    return;
                }

                console.log('✅ 找到贴文元素:', postElement);

                // 先滚动到贴文位置
                postElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // 如果有具体的评论回复ID，则进一步定位到具体评论
                if (commentReplyId) {
                    // 等待滚动完成后再定位具体评论
                    setTimeout(() => {
                        this.scrollToSpecificComment(postElement, commentReplyId);
                    }, 800); // 给滚动动画足够时间
                } else {
                    // 如果没有具体评论ID，就滚动到贴文的评论区域
                    setTimeout(() => {
                        const commentBlock = postElement.querySelector('.comment-block');
                        if (commentBlock) {
                            commentBlock.scrollIntoView({
                                behavior: 'smooth',
                                block: 'nearest'
                            });
                        }
                        // 重置标志位
                        this.isScrollingToComment = false;
                    }, 800);
                }

            } catch (error) {
                console.error('滚动到评论时出错:', error);
                this.isScrollingToComment = false;
            }
        },

        // 滚动到具体的评论回复
        async scrollToSpecificComment(postElement, commentReplyId, retryCount = 0) {
            const maxRetries = 3; // 最大重试次数

            try {
                // 在贴文内查找具体的评论回复
                const commentElement = postElement.querySelector(`[commentReplyId="${commentReplyId}"]`);

                if (commentElement) {
                    console.log('✅ 找到具体评论元素:', commentElement);

                    // 滚动到具体评论
                    commentElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });

                    // 添加高亮效果
                    this.highlightComment(commentElement);

                    // 重置标志位
                    this.isScrollingToComment = false;
                    return; // 成功找到，退出
                }

                // 如果没找到评论且还有重试次数
                if (retryCount < maxRetries) {
                    console.warn(`未找到 commentReplyId 为 ${commentReplyId} 的评论，尝试加载更多 (${retryCount + 1}/${maxRetries})`);

                    const commentBlock = postElement.querySelector('.comment-block');
                    if (commentBlock) {
                        // 检查是否还有更多评论可以加载
                        const hasMoreComments = this.checkHasMoreComments(postElement);

                        if (hasMoreComments) {
                            // 滚动到评论区底部，触发自动加载更多
                            commentBlock.scrollTop = commentBlock.scrollHeight;

                            // 等待加载完成后重试
                            setTimeout(() => {
                                this.scrollToSpecificComment(postElement, commentReplyId, retryCount + 1);
                            }, 2000); // 增加等待时间
                        } else {
                            console.warn(`评论 ${commentReplyId} 可能不存在或已被删除`);
                            // 滚动到评论区域作为备选方案
                            commentBlock.scrollIntoView({
                                behavior: 'smooth',
                                block: 'nearest'
                            });
                            // 重置标志位
                            this.isScrollingToComment = false;
                        }
                    }
                } else {
                    console.error(`已达到最大重试次数，无法找到评论 ${commentReplyId}`);
                    // 最后的备选方案：滚动到评论区域
                    const commentBlock = postElement.querySelector('.comment-block');
                    if (commentBlock) {
                        commentBlock.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }
                    // 重置标志位
                    this.isScrollingToComment = false;
                }
            } catch (error) {
                console.error('滚动到具体评论时出错:', error);
                // 重置标志位
                this.isScrollingToComment = false;
            }
        },

        // 检查是否还有更多评论可以加载
        checkHasMoreComments(postElement) {
            // 查找加载提示或者通过其他方式判断是否还有更多评论
            const loadingTip = postElement.querySelector('.scroll-loading-tip');
            const noMoreTip = postElement.querySelector('.no-more-tip');

            // 如果显示"没有更多评论了"，则表示没有更多了
            if (noMoreTip && noMoreTip.style.display !== 'none') {
                return false;
            }

            // 如果正在加载，暂时认为还有更多
            if (loadingTip && loadingTip.style.display !== 'none') {
                return true;
            }

            // 默认认为可能还有更多（保守策略）
            return true;
        },

        // 高亮评论元素
        highlightComment(element) {
            // 添加高亮样式
            element.style.backgroundColor = '#fff3cd';
            element.style.border = '2px solid #ffc107';
            element.style.borderRadius = '4px';
            element.style.transition = 'all 0.3s ease';

            // 3秒后移除高亮
            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.border = '';
                element.style.borderRadius = '';
                element.style.transition = '';
            }, 3000);
        },

        async loadMore() {
            try {
                if (this.pagination.loading || !this.pagination.hasMore) return

                const container = this.$el.querySelector('.scroll-block')
                // 记录当前滚动位置
                const oldScrollTop = container.scrollTop
                const oldScrollHeight = container.scrollHeight

                this.pagination.loading = true
                this.pagination.page++

                // 模拟分页切片（后期可替换为API请求）
                const end = -((this.pagination.page - 1) * this.pagination.pageSize)
                const start = end - this.pagination.pageSize
                const newData = this.originData.slice(start, end)

                // 仅加载可视区域的附件
                await processBatchFiles(newData, this.videoTypes);

                this.visibleOptions = [...newData, ...this.visibleOptions]
                this.pagination.hasMore = -start < this.originData.length
                this.pagination.loading = false



                // 加载完成后强制检查是否需要继续加载
                this.$nextTick(() => {
                    const container = this.$el.querySelector('.scroll-block')
                    if (!container) return

                    const { scrollHeight, clientHeight } = container
                    // 如果内容高度不足容器高度，自动加载下一页
                    if (scrollHeight <= clientHeight * 1.5 && this.pagination.hasMore) {
                        this.loadMore()
                    }
                })

                // 等待数据和DOM更新
                await this.$nextTick()

                // 计算高度变化量并设置新位置
                const newScrollHeight = container.scrollHeight
                container.scrollTop = oldScrollTop + (newScrollHeight - oldScrollHeight)

            } finally {
                this.pagination.loading = false
            }
        },

        // 评论
        handelComment(item) {
            if (this.detailInfo.roleInfo == 'moderator') {
                return this.$message.error('主持人无法评论')
            }
            if (this.detailInfo.roleInfo == 'expert') {
                return this.$message.error('专家无法评论')
            }
            if (this.detailInfo.roleInfo == 'spectator' || this.detailInfo.roleInfo == '') {
                return this.$message.error('观众无法评论')
            }

            if (this.detailInfo.teamName == '红队' && this.redSeconds <= 0) {
                return this.$message.error('红队倒计时已结束，无法评论')
            }
            if (this.detailInfo.teamName == '蓝队' && this.blueSeconds <= 0) {
                return this.$message.error('蓝队倒计时已结束，无法评论')
            }

            // 重置表单数据，确保每次打开都是干净的状态
            this.commentForm = { content: '' };
            this.uploadFileList = [];
            this.nowCommentInfo = item;
            this.commentDialog = true;
        },
        // 评论提交
        handleCommentSubmit(formData) {
            if (this.detailInfo.teamName == '红队' && this.redSeconds <= 0) {
                return this.$message.error('红队倒计时已结束，无法评论')
            }
            if (this.detailInfo.teamName == '蓝队' && this.blueSeconds <= 0) {
                return this.$message.error('蓝队倒计时已结束，无法评论')
            }

            let params = {
                ...formData,
                drillTaskId: this.drillTaskId,//演练任务ID
                processStageId: this.currentStep,//所属阶段ID

                commentType: '7',
                roleInfo: this.detailInfo.roleInfo,//角色类型
                teamType: this.detailInfo.teamName == '红队' ? 1 : 2,//队伍类型
                isCaptain: (this.detailInfo.roleInfo == 'redCap' || this.detailInfo.roleInfo == 'blueCap'),//是否队长发布（0:否 1:是）
                rootId: this.nowCommentInfo.rootId || '',

                replyToUserId: this.nowCommentInfo.userId || '',
                commentId: this.nowCommentInfo.commentId,//爆料贴文的id
                parentId: this.nowCommentInfo.commentReplyId || 0,
            }

            commentReplyAddApi(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('评论发布成功');

                    // 关闭对话框
                    this.commentDialog = false;
                    // 重置表单数据
                    this.commentForm = { content: '' };
                    this.uploadFileList = [];

                    // 依赖WebSocket推送来更新评论列表，不需要手动刷新
                }
            }).catch(error => {
                console.error('评论发布失败:', error);
                this.$message.error('评论发布失败，请稍后重试');
            })
        },

        // 点赞
        handelLike(item) {
            if (this.detailInfo.roleInfo == 'moderator') {
                return this.$message.error('主持人无法点赞')
            }
            if (this.detailInfo.roleInfo == 'expert') {
                return this.$message.error('专家无法点赞')
            }
            if (this.detailInfo.roleInfo == 'spectator' || this.detailInfo.roleInfo == '') {
                return this.$message.error('观众无法点赞')
            }

            if (this.detailInfo.teamName == '红队' && this.redSeconds <= 0) {
                return this.$message.error('红队倒计时已结束，无法点赞')
            }
            if (this.detailInfo.teamName == '蓝队' && this.blueSeconds <= 0) {
                return this.$message.error('蓝队倒计时已结束，无法点赞')
            }

            let params = {
                drillTaskId: item.drillTaskId,
                processStageId: this.currentStep,//所属阶段ID
                teamType: this.detailInfo.teamName == '红队' ? 1 : 2,//队伍类型,
                commentType: item.commentType,
                roleInfo: this.detailInfo.roleInfo,
                commentId: item.commentId,
                comment: item.content,// 点赞内容
            }

            if (item.commentType == '7') {// 回复评论
                params.commentReplyId = item.commentReplyId
            } else {
                params.commentReplyId = item.commentId
            }

            let reqApi = item.isLike ? commentLikeCancelApi : commentLikeAddApi
            reqApi(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('操作成功')
                }
            })
        },

        //放大内容
        amplifyElement(item, e) {
            // 阻止事件冒泡
            e.stopPropagation();

            // 保存当前项目的引用，用于在弹窗中重新渲染组件
            this.amplifyItem = item;

            // 如果是爆料贴文，传递当前的排序状态
            if (item.commentType === '2') {
                this.amplifyCurrentSortBy = this.postSortStates[item.commentId] || 'createdTime';
            }

            // 获取当前点击元素所在的timeline-item
            const timelineItem = e.target.closest('.timeline-item');
            if (!timelineItem) return;

            // 获取该timeline-item中的timelineBody元素
            const timelineBody = timelineItem.querySelector('.timeline-body');
            if (!timelineBody) return;

            // 将timelineBody的内容复制到弹窗中（用于非爆料贴文类型）
            this.amplifyContent = timelineBody.innerHTML;

            // 强制更新组件以确保使用最新数据
            this.amplifyUpdateKey++;

            // 显示弹窗
            this.amplifyDialog = true;
        },

        // 放大/缩小二维码
        amplifyQrcode() {
            // 切换二维码放大状态
            this.qrcodeEnlarged = !this.qrcodeEnlarged;
        },

        // 切换布局模式（单列/双列）
        toggleLayout() {
            this.isDoubleColumn = !this.isDoubleColumn;
        },

        // 切换弹幕开关
        toggleDanmaku() {
            this.isDanmakuEnabled = !this.isDanmakuEnabled;
            if (this.isDanmakuEnabled) {
                this.$message.success('弹幕已开启');
            } else {
                this.$message.success('弹幕已关闭');
            }
        },

        // 等待数据初始化完成的方法
        waitForDataInit() {
            return new Promise((resolve, reject) => {
                if (this.dataInitialized) {
                    resolve();
                    return;
                }

                let checkInterval = null;
                let timeoutId = null;

                // 清理函数
                const cleanup = () => {
                    if (checkInterval) {
                        clearInterval(checkInterval);
                        checkInterval = null;
                    }
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                        timeoutId = null;
                    }
                };

                // 使用轮询检查数据是否初始化完成
                checkInterval = setInterval(() => {
                    if (this.dataInitialized) {
                        cleanup();
                        resolve();
                    }
                }, 100); // 每100ms检查一次

                // 设置超时，避免无限等待
                timeoutId = setTimeout(() => {
                    cleanup();
                    console.warn('数据初始化超时，可能存在网络问题或数据加载异常');
                    // 超时时返回 reject 而不是 resolve，让调用方知道超时了
                    reject(new Error('数据初始化超时'));
                }, 15000); // 增加到15秒超时，给网络慢的情况更多时间

                // 组件销毁时的清理（防止内存泄漏）
                this.$once('hook:beforeDestroy', cleanup);
            });
        },
    }
}
</script>
<style scoped lang="scss">
.vsScreen {
    overflow-y: auto; // 允许内部滚动
    font-size: 1rem; // 继承容器设置的基准字体大小

    width: 100%;
    height: 100%;
    // background: #f4f7f9;
    background-image: url("../../assets/images/simulatedVS/vsScreen-BG.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;

    // 弹幕容器样式
    .danmaku-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        // 高度设置为只覆盖上方区域（top-title + top-notice + countdown-block）
        height: 50vh; // 使用em单位，与页面其他元素保持一致
        z-index: 100; // 设置为比布局切换按钮（z-index: 1000）低的值，但比其他元素高
        pointer-events: none; // 确保不影响下层按钮的点击
        overflow: hidden;
    }

    // 布局切换按钮样式
    .layout-toggle {
        position: absolute;
        top: 22em;
        left: 20em;
        z-index: 1000;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 3em;
        padding: 3em;
        display: flex;
        // gap: 10px;
        cursor: pointer;

        span {
            font-size: 14em;
            color: #999;
            padding: 0.4em 0.7em;
            border-radius: 0.3em;
            // transition: all 0.3s;

            &.active {
                color: #fff;
                background-color: rgba(255, 255, 255, 0.2);
            }
        }

        &:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }
    }

    // 弹幕开关样式
    .danmaku-toggle {
        position: absolute;
        top: 60em;
        left: 22em;
        z-index: 1000;
        cursor: pointer;
        transition: opacity 0.3s ease;

        img {
            width: 35em;
            height: auto;
            display: block;
        }

        &:hover {
            opacity: 0.8;
        }
    }

    // 修改为弹性盒子布局
    display: flex;
    flex-direction: column; // 垂直排列

    font-family: PingFangSC,
    PingFang SC;
    position: relative;

    overflow-x: hidden;
    /* 同时隐藏xy方向溢出 */
    contain: strict;
    /* 启用严格布局约束 */

    .qrcode-block {
        position: absolute;
        top: 22em;
        right: 40em;
        z-index: 99;

        .qrcode {
            border: 5em solid #FFFFFF;
            margin-bottom: 5em;

            ::v-deep {
                canvas {
                    width: 70em !important;
                    height: 70em !important;
                }
            }
        }

        .qrcode-tips {
            font-size: 12em;
            color: #FFFFFF;
            text-align: center;
        }
    }


    // 顶部区块设置固定高度
    .top-title,
    .top-notice,
    .score-block {
        flex-shrink: 0; // 禁止压缩
    }


    .top-title {
        width: 100%;
        aspect-ratio: 24;
        background-image: url("../../assets/images/simulatedVS/vsScreen-top-bg.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        color: #FFFFFF;
        font-size: 25em;
        // font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .titleMain {
        width: 30%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        position: relative;

        .title-text {
            white-space: nowrap;
            transition: transform 0.3s ease;
        }

        // 当标题需要滚动时
        &.title-scrolling {
            .title-text {
                animation: titleScroll 12s linear infinite;
            }
        }

        // 当标题不需要滚动时，确保居中显示
        &:not(.title-scrolling) {
            text-align: center;

            .title-text {
                width: 100%;
                text-align: center;
            }
        }
    }

    // 标题滚动动画
    @keyframes titleScroll {
        0% {
            transform: translateX(100%);
        }

        100% {
            transform: translateX(-100%);
        }
    }

    .top-notice {
        width: 60%;
        margin: 0 auto;
        background-image: url("../../assets/images/simulatedVS/vsScreen-info-bg.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;

        text-align: center;
        font-size: 12em;
        color: #FFFFFF;
        padding: 0.5em 1.2em;
        margin-top: 0.8em;
        overflow: hidden;

        .scroll-text {
            // display: inline-block;
            white-space: nowrap;
            // animation: scrollText 10s linear infinite;
            animation: scrollText linear infinite;
        }

        @keyframes scrollText {
            0% {
                transform: translateX(100%);
            }

            100% {
                transform: translateX(-100%);
            }
        }
    }

    .countdown-block {
        width: 100%;
        font-size: 14em;
        color: #FFFFFF;
        display: flex;
        // justify-content: space-around;
        justify-content: space-evenly;
        // padding: 0.2em 0 1.3em 0;
        // padding-bottom: 0.5em;
        // padding-bottom: 2.1em;

        .top-score {
            position: relative;
            font-size: 0.0714285714em;

            .top-score-item {
                position: absolute;
                display: flex;
                align-items: center;
                justify-content: center;

                .top-score-title {
                    font-size: 14em;
                    width: max-content;
                }

                .top-score-value {
                    font-size: 36em;
                    width: max-content;
                    font-family: SourceHanSansCN, SourceHanSansCN;
                    text-align: center;
                    font-style: normal;
                    -webkit-text-stroke: 0.02em #FFFFFF;
                    font-weight: 600;
                }
            }

            .blueScore {
                right: 1vw;

                .top-score-value {
                    color: #0543C6;
                }
            }

            .redScore {
                left: 1vw;

                .top-score-value {
                    color: #D41503;
                }
            }
        }

    }

    .statistics-block {
        width: 100%;
        font-size: 14em;
        display: flex;
        color: #FFFFFF;
        justify-content: space-around;
        padding: 1em 0;

        .statistics-block-item {

            .statistics-title {}

            .statistics-num {
                margin-right: 1em;
                font-size: 1.2em;
                font-family: SourceHanSansCN, SourceHanSansCN;
                text-align: center;
                font-style: normal;
                -webkit-text-stroke: 0.02em #FFFFFF;
                font-weight: 600;
            }
        }


    }

    .score-block {
        display: flex;
        justify-content: space-around;
        align-items: center;
        position: relative;
        font-size: 16em;
        color: #FFFFFF;
        margin-top: 1em;
        // margin-bottom: 1em;
        margin-bottom: 3em;

        .score-item {
            width: 5em;
            display: flex;
            flex-direction: column;
            align-items: center;

            .score-item-title {
                color: #FFFFFF;
                margin-bottom: 0.3em;
                font-size: 0.8em;
            }

            .score-item-value {
                font-size: 1.4em;
                // width: 5em;
                // height: 5em;
                width: 6em;
                height: 6em;
                display: flex;
                align-items: center;
                justify-content: center;
                background-repeat: no-repeat;
                background-position: center;
                background-size: 100% 100%;
            }

            .blueScore {
                background-image: url("../../assets/images/simulatedVS/score-point-blue.svg");
            }

            .redScore {
                background-image: url("../../assets/images/simulatedVS/score-point-red.svg");

            }
        }

        .centerLogo {
            font-size: 1.2em;
            font-weight: 600;
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;

            img {
                width: 6em;
                margin-top: 1em;
            }
        }
    }



    .scroll-block {
        flex: 1; // 占据剩余空间
        min-height: 5em; // 最小高度保护
        height: 0; // 关键：允许flex压缩

        overflow-anchor: none;
        /* 禁用浏览器自动滚动锚定 */
        will-change: transform;
        /* 优化滚动性能 */

        overflow-y: auto; // 启用垂直滚动

        // 美化滚动条
        &::-webkit-scrollbar {
            width: 6em; // 滚动条宽度
            background-color: transparent; // 轨道背景
        }

        &::-webkit-scrollbar-thumb {
            border-radius: 3em;
            background: rgba(255, 255, 255, 0.3);
        }

        .checkStageTypeBtn {
            width: 100%;
            text-align: center;
            padding: 10em 0;
        }
    }


    .scoreDetail-block {
        font-size: 14em;
        width: 100%;
        color: #FFFFFF;
        display: flex;
        justify-content: space-around;

        .scoreDetail-item {
            flex: 1;
            // padding: 0 3em;
            padding: 0 6em;

            .detail-item {
                display: flex;

                // justify-content: center;
                .detail-process {
                    flex: 1;
                    background-color: #0543C6;
                }

                .detail-option {
                    flex: 2;

                    .option-item {
                        margin-bottom: 0.2em;
                    }
                }

                .detail-score {
                    flex: 1;
                }

                .detail-process,
                .detail-option,
                .detail-score {
                    padding: 1em;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    margin: 0 0.5em 0.5em 0;

                    height: 5.5em; //固定高度
                }

                .detail-option,
                .detail-score {
                    background-image: url("../../assets/images/simulatedVS/infoBorder-blue.png");
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 100% 100%;
                }

                .detail-option {
                    background-image: url("../../assets/images/simulatedVS/infoBorder-blue-long.png");
                }
            }
        }

        .red-scoreDetail {

            .detail-process {
                background-color: #D41503 !important;
            }

            .detail-score {
                background-image: url("../../assets/images/simulatedVS/infoBorder-red.png") !important;
            }

            .detail-option {
                background-image: url("../../assets/images/simulatedVS/infoBorder-red-long.png");
            }
        }

    }

    .whatHappened-block {
        // 新增高度限制
        height: fit-content;
        // min-height: 100%; // 保持等高布局

        position: relative;
        z-index: 1;
        /* 确保内容在弹幕下方 */
    }



    .whatHappened-block {
        color: #FFFFFF;

        *,
        *:after,
        *:before {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
        }

        position: relative;

        background: url("../../assets/images/simulatedVS/whiteLine.png");
        background-repeat: repeat-y;
        background-position: 50% 0;
        background-size: 1em;
        overflow: auto;

        // 单列模式样式
        &.single-column {
            background: none;

            .timeline-item,
            .timeline-item-right {
                float: none;
                clear: both;
                width: 80%;
                margin: 10em auto;
                font-size: 1.2em; //放大

                &:after {
                    display: none;
                }
            }
        }

        .timeline-item {
            position: relative;
            float: left;
            clear: left;
            width: 45%;
            margin: 10em 2.5%;
            z-index: 15;

            &:after {
                // display: block;
                // content: " ";
                // height: 9px;
                // width: 9px;
                // background: #666666;
                // // border-radius: 50%;
                // position: absolute;
                // right: -5%;
                // top: 1.5em;


                content: "";
                position: absolute;
                right: -5.4%;
                /* 调整位置 */
                top: 20em;
                width: 0;
                height: 0;
                border-top: 10em solid transparent;
                border-bottom: 10em solid transparent;
                border-right: 10em solid #0543C6;
                /* 蓝色小三角 */
            }

            .timeline-top {
                font-size: 10em;
                display: flex;
                justify-content: space-between;
                align-items: end;

                .top-role {
                    padding: 0.1em 1em;
                    background-image: url("../../assets/images/simulatedVS/blueRectangle.png");
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: 100% 100%;
                }

                .top-time {
                    /* 时间显示样式 */
                    color: rgba(255, 255, 255, 0.8);
                }
            }

            .timeline-content {
                background: #02004D;
                box-shadow: inset 0 0 20em 0 rgba(17, 40, 255, 0.66);
                border: 2em solid #0543C6;

                .instruction-body {
                    font-size: 13em;
                    padding: 0.5em 0.5em;
                    display: flex;
                    align-items: baseline;

                    .instruction-Num {
                        color: #66FFFF;
                        flex-shrink: 0;
                        margin-right: 1em;
                    }

                    .instruction-content {
                        overflow-wrap: break-word;
                        flex: 1;
                        width: 38em;
                    }
                }

                .timeline-content-title {
                    font-size: 13em;
                    padding: 0.4em 0.5em;
                    border-bottom: 0.01em solid #709EFF;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .instruction-status {
                        display: flex;
                        align-items: center;

                        img {
                            height: 1.3em;
                            margin-right: 0.5em;
                        }
                    }

                    .instruction-box {
                        background-image: url("../../assets/images/simulatedVS/instructionBlue.png");
                        padding: 0.3em 0.5em 0.3em 2em;
                        background-repeat: no-repeat;
                        background-position: bottom;
                        background-size: 100% 100%;
                    }

                    .amplifyBtn {
                        cursor: pointer;
                        width: 1.5em;
                        height: 1.5em;
                        margin-left: 0.5em;
                        background-image: url("../../assets/images/simulatedVS/amplifyIcon.svg");
                        background-repeat: no-repeat;
                        background-position: bottom;
                        background-size: 100% 100%;

                        &:hover {
                            background-image: url("../../assets/images/simulatedVS/amplifyIcon-hover.svg");
                        }
                    }

                    .related-content-box {
                        // padding: 0.3em 0.5em 0.3em 1.5em;
                        margin-right: 0.5em;
                        // background-image: url("../../assets/images/simulatedVS/instructionBlue.png");
                        // background-repeat: no-repeat;
                        // background-position: bottom;
                        // background-size: 100% 100%;
                    }
                }

                .timeline-body {
                    padding: 8em;

                    .creater-box {
                        font-size: 8em;
                        margin-top: 1em;
                        text-align: end;
                    }
                }

            }
        }

        .timeline-item-right {
            float: right;
            clear: right;

            &:after {
                left: -5.4%;
                /* 调整位置 */
                right: auto;
                border-right: none;
                border-left: 10em solid #D41503;
                /* 红色小三角 */
            }

            .timeline-top {
                .top-role {
                    background-image: url("../../assets/images/simulatedVS/redRectangle.png");
                }
            }

            .timeline-content {
                background: #4A0303;
                box-shadow: inset 0 0 20em 0 rgba(212, 21, 3, 0.66);
                border: 2em solid #D41503;

                .timeline-content-title {
                    border-bottom: 0.01em solid #FF948A;

                    .instruction-box {
                        background-image: url("../../assets/images/simulatedVS/instructionRed.png");
                    }

                    .related-content-box {
                        background-image: url("../../assets/images/simulatedVS/instructionRed.png");
                    }
                }
            }
        }

        .marTop {
            margin-top: 40em !important;
        }

    }

}


/* 放大内容弹窗样式 */
.amplify-dialog {
    ::v-deep .el-dialog {
        background: #f4f7f9;
        border-radius: 0.5em;

        .el-dialog__header {
            background: #0543C6;
            padding: 1em 1.5em;
            border-radius: 0.5em 0.5em 0 0;

            .el-dialog__title {
                color: #FFFFFF;
                font-size: 1.2em;
                font-weight: 600;
            }

            .el-dialog__headerbtn .el-dialog__close {
                color: #FFFFFF;
            }
        }

        .el-dialog__body {
            padding: 1.5em;
            max-height: 85vh;
            overflow-y: auto;
        }
    }

    .amplify-content {
        font-size: 0.15em;
        /* 放大内容 */

        /* 保持原有样式但适当放大 */
        .template1,
        .template2,
        .template3,
        .template4,
        .template5,
        .template-fxbg,
        .template-bltw,
        .templateZJ {
            // transform: scale(1.2);
            transform-origin: top left;
            // margin-bottom: 20px;
        }

        /* 确保图片正确显示 */
        img {
            max-width: 100%;
        }

        /* 确保视频正确显示 */
        video {
            max-width: 100%;
            height: auto;
        }
    }
}

/* 二维码放大弹窗样式 */
.qrcode-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.qrcode-enlarged {
    background-color: #FFFFFF;
    padding: 30em;
    border-radius: 10em;
    text-align: center;

    ::v-deep {
        canvas {
            width: 300em !important;
            height: 300em !important;
        }
    }

    .qrcode-tips {
        font-size: 20em;
        color: #333333;
        margin-top: 0.6em;
        font-weight: bold;
    }

    .qrcode-close-hint {
        font-size: 14em;
        color: #666666;
        margin-top: 0.5em;
    }

    /* 确保二维码在弹窗中居中 */
    ::v-deep canvas {
        margin: 0 auto;
    }
}


/* Loading 遮罩层样式 */
.vs-screen-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);

    .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        font-size: 16em;

        .loading-icon {
            font-size: 2em;
            margin-bottom: 0.5em;
            animation: rotating 2s linear infinite;
        }

        .loading-text {
            font-size: 1em;
            font-weight: 500;
            text-align: center;
        }
    }
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 手机竖屏模式 */
@media screen and (max-width: 992px) and (orientation: portrait) {
    .vsScreen .scroll-block {
        font-size: 1.4em;
    }

    .amplify-dialog .amplify-content {
        font-size: 0.08em;
        /* 放大内容 */
    }
}
</style>
