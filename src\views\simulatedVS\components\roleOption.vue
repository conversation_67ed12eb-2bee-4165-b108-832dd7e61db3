<template>
    <div class="roleOption">
        <div class="role-title">
            <div class="role-info">
                <!-- 主持人 -->
                <template v-if="detailInfo.roleInfo=='moderator'">
                    <div class="main-role">
                        <img style="height: 17px;" src="@/assets/images/simulatedVS/compere.png" alt="">
                        主持人
                    </div>
                </template>
                <!-- 红队队长 -->
                <template v-if="detailInfo.roleInfo=='redCap'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        红队队长：{{detailInfo.redCaptainUser.nickName||''}}
                    </div>
                    <div v-if="getMemberName(detailInfo.redMemberUser)" class="other-role">
                        队员：{{getMemberName(detailInfo.redMemberUser)}}
                    </div>
                </template>
                <!-- 红队队员 -->
                <template v-if="detailInfo.roleInfo=='redMem'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        队员：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.redCaptainUser && detailInfo.redCaptainUser.nickName" class="other-role">
                        红队队长：{{detailInfo.redCaptainUser.nickName}}
                    </div>
                </template>

                <!-- 红队组长 -->
                <template v-if="detailInfo.roleInfo=='redGroupLeader'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        红队组长：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.redCaptainUser && detailInfo.redCaptainUser.nickName" class="other-role">
                        红队队长：{{detailInfo.redCaptainUser.nickName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupName" class="other-role">
                        所属小组：{{currentUserGroup.groupName}}
                    </div>
                    <div v-if="getGroupMemberName(currentUserGroup)" class="other-role">
                        组员：{{getGroupMemberName(currentUserGroup)}}
                    </div>
                </template>
                <!-- 红队组员 -->
                <template v-if="detailInfo.roleInfo=='redGroupMember'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        红队组员：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.redCaptainUser && detailInfo.redCaptainUser.nickName" class="other-role">
                        红队队长：{{detailInfo.redCaptainUser.nickName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupName" class="other-role">
                        所属小组：{{currentUserGroup.groupName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupLeader && currentUserGroup.groupLeader.nickName"
                        class="other-role">
                        组长：{{currentUserGroup.groupLeader.nickName}}
                    </div>
                </template>
                <!-- 蓝队队长 -->
                <template v-if="detailInfo.roleInfo=='blueCap'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        蓝队队长：{{detailInfo.blueCaptainUser.nickName||''}}
                    </div>
                    <div v-if="getMemberName(detailInfo.blueMemberUser)" class="other-role">
                        队员：{{getMemberName(detailInfo.blueMemberUser)}}
                    </div>
                </template>
                <!-- 蓝队队员 -->
                <template v-if="detailInfo.roleInfo=='blueMem'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        队员：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.blueCaptainUser && detailInfo.blueCaptainUser.nickName" class="other-role">
                        蓝队队长：{{detailInfo.blueCaptainUser.nickName}}
                    </div>
                </template>
                <!-- 蓝队组长 -->
                <template v-if="detailInfo.roleInfo=='blueGroupLeader'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        蓝队组长：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.blueCaptainUser && detailInfo.blueCaptainUser.nickName" class="other-role">
                        蓝队队长：{{detailInfo.blueCaptainUser.nickName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupName" class="other-role">
                        所属小组：{{currentUserGroup.groupName}}
                    </div>
                    <div v-if="getGroupMemberName(currentUserGroup)" class="other-role">
                        组员：{{getGroupMemberName(currentUserGroup)}}
                    </div>
                </template>
                <!-- 蓝队组员 -->
                <template v-if="detailInfo.roleInfo=='blueGroupMember'">
                    <div class="main-role">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        蓝队组员：{{nickName||''}}
                    </div>
                    <div v-if="detailInfo.blueCaptainUser && detailInfo.blueCaptainUser.nickName" class="other-role">
                        蓝队队长：{{detailInfo.blueCaptainUser.nickName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupName" class="other-role">
                        所属小组：{{currentUserGroup.groupName}}
                    </div>
                    <div v-if="currentUserGroup && currentUserGroup.groupLeader && currentUserGroup.groupLeader.nickName"
                        class="other-role">
                        组长：{{currentUserGroup.groupLeader.nickName}}
                    </div>
                </template>
                <!-- 游客 -->
                <template v-if="detailInfo.roleInfo=='spectator'">
                    <div class="main-role">
                        游客
                    </div>
                </template>
                <!-- 专家 -->
                <template v-if="detailInfo.roleInfo=='expert'">
                    <div class="main-role">
                        <img style="height: 17px;" src="@/assets/images/simulatedVS/expert.png" alt="">
                        专家
                    </div>
                </template>
            </div>
            <div class="notice" @click="openNotice">
                <div v-if="unreadCount>0" class="notice-num">{{ unreadCount > 99 ? '99+': unreadCount }}</div>
            </div>
        </div>

        <div class="option-Tab">
            <div :class="['tab-item', { 'active-tab': item.value === activeTab }]" v-for="item in tabsList"
                :key="item.value" @click="tabClick(item)">
                {{ item.label }}
            </div>
        </div>
        <div class="option-Main">
            <!-- 流程操作 -->
            <ProcessOption v-show="activeTab === '1'" :detailInfo="detailInfo" :current-step="currentStep"
                :next-step="nextStep" @setStep="setStep"></ProcessOption>

            <!-- 人员信息 -->
            <div v-show="activeTab === '2'" class="personInfo">
                <div v-show="detailInfo.roleInfo&&detailInfo.roleInfo.slice(0, 3)!=='red'" class="team-block" style="margin-bottom: 20px;">
                    <div class="team-title">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        蓝队
                    </div>
                    <div class="person-list">
                        <div v-if="detailInfo.blueCaptainUser && detailInfo.blueCaptainUser.nickName"
                            style="margin-bottom: 10px;">
                            队长：{{detailInfo.blueCaptainUser.nickName}}
                        </div>
                        <div v-if="getMemberName(detailInfo.blueMemberUser)" style="margin-bottom: 10px;">
                            队员：{{getMemberName(detailInfo.blueMemberUser)}}
                        </div>
                        <!-- 蓝队小组信息 -->
                        <div v-for="(group, index) in detailInfo.blueGroups" :key="group.groupId" class="group-info">
                            <div class="group-title">{{group.groupName}}</div>
                            <div class="group-details">
                                <div v-if="group.groupLeader && group.groupLeader.nickName" style="margin-bottom: 5px;">
                                    组长：{{group.groupLeader.nickName}}
                                </div>
                                <div style="margin-bottom: 5px;">
                                    组员：{{getGroupMemberName(group) || '暂无'}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="detailInfo.roleInfo&&detailInfo.roleInfo.slice(0, 4)!=='blue'" class="team-block">
                    <div class="team-title">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        红队
                    </div>
                    <div class="person-list">
                        <div v-if="detailInfo.redCaptainUser && detailInfo.redCaptainUser.nickName"
                            style="margin-bottom: 10px;">
                            队长：{{detailInfo.redCaptainUser.nickName}}
                        </div>
                        <div v-if="getMemberName(detailInfo.redMemberUser)" style="margin-bottom: 10px;">
                            队员：{{getMemberName(detailInfo.redMemberUser)}}
                        </div>
                        <!-- 红队小组信息 -->
                        <div v-for="(group, index) in detailInfo.redGroups" :key="group.groupId" class="group-info">
                            <div class="group-title">{{group.groupName}}</div>
                            <div class="group-details">
                                <div v-if="group.groupLeader && group.groupLeader.nickName" style="margin-bottom: 5px;">
                                    组长：{{group.groupLeader.nickName}}
                                </div>
                                <div style="margin-bottom: 5px;">
                                    组员：{{getGroupMemberName(group) || '暂无'}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发布指令 -->
            <div v-show="activeTab === '3'">
                <el-input type="textarea" maxlength="100" show-word-limit :autosize="{ minRows: 8, maxRows: 10}"
                    placeholder="请输入指令" v-model.trim="instruction">
                </el-input>
                <div style="display: flex; justify-content: flex-end;margin-top: 20px;">
                    <el-button size="small" @click="instruction=''">置空</el-button>
                    <el-button :disabled="submitDisable" :loading="instructionLoading" size="mini" type="primary"
                        @click="submitInstruction">发布</el-button>
                </div>
            </div>

            <!-- 发布评论 -->
            <div v-show="activeTab === '4'">
                <PublicComment :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep">
                </PublicComment>
            </div>

            <!-- 专家点评 -->
            <div v-show="activeTab === '5'">
                <!-- 蓝队点评 -->
                <CommentTeam :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep" :teamType="2">
                </CommentTeam>
            </div>
            <div v-show="activeTab === '6'">
                <!-- 红队点评 -->
                <CommentTeam :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep" :teamType="1">
                </CommentTeam>
            </div>
        </div>
        <el-dialog class="dialog__wrapper notice-dialog" title="通知中心" :visible.sync="noticeDialog" width="600px">
            <!-- 顶部筛选下拉菜单 -->
            <div class="notice-header">
                <div class="filter-controls">
                    <!-- 通知类型筛选 -->
                    <div class="filter-dropdown">
                        <el-dropdown @command="handleFilterCommand" trigger="click">
                            <span class="dropdown-trigger">
                                {{ getCurrentFilterText() }}
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-for="category in enabledCategories" :key="category.command"
                                    :command="category.command">
                                    <i :class="category.icon"></i>
                                    {{ category.text }}
                                    <span class="count" v-if="getCategoryCount(category.command) > 0">
                                        ({{ getCategoryCount(category.command) }})
                                    </span>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>

                    <!-- 已读状态筛选 -->
                    <div class="filter-dropdown read-status-filter">
                        <el-dropdown @command="handleReadStatusCommand" trigger="click">
                            <span class="dropdown-trigger">
                                {{ getCurrentReadStatusText() }}
                                <i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-for="status in readStatusOptions" :key="status.value"
                                    :command="status.value">
                                    <i :class="status.icon"></i>
                                    {{ status.text }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>

                <div class="header-actions">
                    <el-button size="small" type="text" @click="markAllRead"
                        :disabled="currentCategoryUnreadCount === 0">
                        全部已读
                    </el-button>
                </div>
            </div>

            <!-- 通知列表 -->
            <div class="notice-list" v-loading="noticeLoading">
                <div class="notice-item" v-for="(item,index) in noticeList" :key="item.id || index"
                    :class="{ 'unread': !item.isRead }" @click="goCommentPlace(item)">
                    <div class="notice-type-badge">
                        <el-tag size="mini" :type="getNoticeTypeColor(item.notificationType)">
                            {{ item.notificationTypeName }}
                        </el-tag>
                    </div>
                    <img :src="getAvatar(item.fromUserAvatar)" alt="用户头像" class="user-avatar">
                    <div class="main-content">
                        <div class="user-name">{{ item.fromUserNickName }}</div>
                        <div class="content">{{ item.content }}</div>
                        <div class="time">{{ item.createdTime }}</div>
                    </div>
                    <div class="notice-actions" v-if="!item.isRead">
                        <el-button size="mini" type="text" @click="markNotificationRead([item.notificationId])">
                            标记已读
                        </el-button>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-if="!noticeLoading && noticeList.length === 0" class="empty-notice">
                <i class="el-icon-bell"></i>
                <p>暂无通知消息</p>
            </div>

            <!-- 分页 -->
            <div class="notice-pagination" v-if="noticeTotal > noticeParams.pageSize">
                <el-pagination @current-change="handlePageChange" :current-page="noticeParams.pageNum"
                    :page-size="noticeParams.pageSize" layout="prev, pager, next" :total="noticeTotal">
                </el-pagination>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="noticeDialog = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import ProcessOption from '@/views/simulatedVS/components/processOption.vue'
import PublicComment from '@/views/simulatedVS/components/publicComment.vue'
import CommentTeam from '@/views/simulatedVS/components/commentTeam.vue'
import {
    drillStageNextApi,
    drillCommentPublishApi,
    getNotificationListApi,
    markNotificationReadApi,
    getNotificationStatisticsApi
} from "@/api/simulatedVS/index.js";
import { WS_EVENTS, safeOn } from '@/utils/eventBus';
export default {
    components: { ProcessOption, PublicComment, CommentTeam },
    props: {
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        steps: {
            type: Array,
            default() {
                return []
            }
        },
        detailInfo: {
            type: Object,
            default() {
                return {
                    redCaptainUser: {},
                    blueCaptainUser: {}
                }
            }
        },
    },
    data() {
        return {
            activeTab: '1',
            instruction: '',
            nowTeamType: 1, // 当前队伍
            instructionLoading: false, // 发布指令按钮loading状态
            noticeDialog: false, // 通知弹窗
            noticeList: [], // 通知列表
            noticeLoading: false, // 通知加载状态
            unreadCount: 0, // 未读通知数量
            noticeParams: {
                drillTaskId: null,
                pageNum: 1,
                pageSize: 10,
                onlyUnread: null,
                notificationType: null,
                orderBy: 'createTime_desc'
            }, // 通知查询参数
            noticeTotal: 0, // 通知总数
            // 各类型通知数量统计
            likeCount: 0,
            mentionCount: 0,
            replyCount: 0,
            systemCount: 0,
            // 通知分类配置
            notificationCategories: [
                {
                    text: '全部消息',
                    icon: 'el-icon-message',
                    command: 'all',
                    type: null,
                    enabled: true,
                    typeText: null,
                    typeColor: null
                },
                {
                    text: '为我点赞',
                    icon: 'el-icon-star-off',
                    command: 'like',
                    type: 3,
                    enabled: false, // 暂时不启用
                    typeText: '点赞通知',
                    typeColor: 'success' // 绿色
                },
                {
                    text: '@通知',
                    icon: 'el-icon-position',
                    command: 'mention',
                    type: 1,
                    enabled: true,
                    typeText: '@通知',
                    typeColor: 'warning' // 橙色
                },
                {
                    text: '回复通知',
                    icon: 'el-icon-chat-line-round',
                    command: 'reply',
                    type: 2,
                    enabled: true,
                    typeText: '回复通知',
                    typeColor: 'primary' // 蓝色
                },
                {
                    text: '系统通知',
                    icon: 'el-icon-bell',
                    command: 'system',
                    type: 4,
                    enabled: false,
                    typeText: '系统通知',
                    typeColor: 'info' // 灰色
                }
            ],
            // 已读状态筛选配置
            readStatusOptions: [
                {
                    text: '全部',
                    icon: 'el-icon-document',
                    value: 'all',
                    onlyUnread: null  // null=全部
                },
                {
                    text: '仅未读',
                    icon: 'el-icon-view',
                    value: 'unread',
                    onlyUnread: true  // true=仅未读
                },
                {
                    text: '仅已读',
                    icon: 'el-icon-check',
                    value: 'read',
                    onlyUnread: false // false=仅已读
                }
            ],
        }
    },
    // "红队队长":"redCap",
    // "红队队员":"redMem",
    // "红队组长":"redGroupLeader",
    // "红队组员":"redGroupMember",
    // "蓝队队长":"blueCap",
    // "蓝队队员":"blueMem",
    // "蓝队组长":"blueGroupLeader",
    // "蓝队组员":"blueGroupMember",
    // "主持人":"moderator",
    // "观众":"spectator",
    // "专家":"expert",
    computed: {
        tabsList() {
            if (this.detailInfo.roleInfo == 'moderator') {
                return [
                    { label: '流程操作', value: '1' },
                    { label: '人员信息', value: '2' },
                ]
            } else if (this.detailInfo.roleInfo == 'expert') {
                return [
                    { label: '蓝队点评', value: '5' },
                    { label: '红队点评', value: '6' },
                ]
            } else if (this.detailInfo.roleInfo == 'redCap' || this.detailInfo.roleInfo == 'blueCap' || this.detailInfo.roleInfo == 'redGroupLeader' || this.detailInfo.roleInfo == 'blueGroupLeader') {
                // 红蓝队长、组长能发布指令
                return [
                    { label: '人员信息', value: '2' },
                    { label: '发布指令', value: '3' },
                    { label: '发布信息', value: '4' },
                ]
            } else if (this.detailInfo.roleInfo == 'redMem' || this.detailInfo.roleInfo == 'blueMem' || this.detailInfo.roleInfo == 'redGroupMember' || this.detailInfo.roleInfo == 'blueGroupMember') {
                // 红蓝队员、组员只能发布信息
                return [
                    { label: '发布信息', value: '4' },
                ]
            } else {
                return [
                    { label: '人员信息', value: '2' }
                ]
            }
        },
        submitDisable() { // 发布按钮的disable
            // return (!this.currentStep || !this.nextStep) || this.selfSeconds <= 0
            return (!this.currentStep) || this.selfSeconds <= 0 || this.instructionLoading
        },
        selfSeconds() { // 当前队伍的倒计时秒数
            if (this.nowTeamType == 1) {
                return this.$parent.$refs['miniVsScreen'].redSeconds
            } else {
                return this.$parent.$refs['miniVsScreen'].blueSeconds
            }
        },
        nickName() {
            console.log('this.$store.state.user :>> ', this.$store.state.user);
            return this.$store.state.user.user.nickName
        },
        // 获取当前用户所在的小组信息
        currentUserGroup() {
            const currentUserId = this.$store.state.user.user.userId
            const roleInfo = this.detailInfo.roleInfo

            if (roleInfo === 'redGroupLeader' || roleInfo === 'redGroupMember') {
                // 在红队小组中查找
                return this.detailInfo.redGroups?.find(group => {
                    // 检查是否是组长
                    if (group.groupLeader && group.groupLeader.userId === currentUserId) {
                        return true
                    }
                    // 检查是否是组员
                    return group.groupMembers?.some(member => member.userId === currentUserId)
                })
            } else if (roleInfo === 'blueGroupLeader' || roleInfo === 'blueGroupMember') {
                // 在蓝队小组中查找
                return this.detailInfo.blueGroups?.find(group => {
                    // 检查是否是组长
                    if (group.groupLeader && group.groupLeader.userId === currentUserId) {
                        return true
                    }
                    // 检查是否是组员
                    return group.groupMembers?.some(member => member.userId === currentUserId)
                })
            }
            return null
        },
        // 获取当前分类的未读数量
        currentCategoryUnreadCount() {
            // 根据当前选中的通知类型返回对应的未读数量
            const type = this.noticeParams.notificationType;

            // 如果是全部消息
            if (type === null) {
                return this.unreadCount;
            }

            // 根据类型返回对应的未读数量
            switch (type) {
                case 1: // @通知
                    return this.mentionCount;
                case 2: // 回复通知
                    return this.replyCount;
                case 3: // 点赞通知
                    return this.likeCount;
                case 4: // 系统通知
                    return this.systemCount;
                default:
                    return this.unreadCount;
            }
        },
        // 获取启用的分类
        enabledCategories() {
            return this.notificationCategories.filter(category => category.enabled);
        },
    },
    watch: {
        tabsList: {
            immediate: true,
            handler(newVal) {
                if (newVal.length > 0) {
                    this.activeTab = newVal[0]?.value;
                }
            }
        },
        detailInfo: {
            immediate: true,
            deep: true,
            handler(newVal) {
                const redRoles = ['redCap', 'redMem', 'redGroupLeader', 'redGroupMember']
                this.nowTeamType = redRoles.includes(newVal.roleInfo) ? 1 : 2
                if (newVal && newVal.drillTaskId) {
                    this.noticeParams.drillTaskId = newVal.drillTaskId;
                    // 初始化通知功能
                    this.getNoticeState();
                }
            },
        },
    },
    created() {
        // 安全监听 WebSocket 消息
        this.unlistenMessage = safeOn(WS_EVENTS.MESSAGE, this.handleMessage);
    },
    mounted() {
    },
    beforeDestroy() {
        // 移除 WebSocket 监听
        if (this.unlistenMessage) {
            this.unlistenMessage();
        }
    },
    methods: {
        tabClick(item) {
            this.activeTab = item.value
        },
        // 发布指令
        submitInstruction() {
            // 防止重复提交
            if (this.instructionLoading) {
                return
            }

            if (!this.instruction) {
                this.$message.error('请输入指令')
                return
            }

            // 开始loading
            this.instructionLoading = true

            let params = {
                drillTaskId: this.detailInfo.drillTaskId,//演练任务ID
                processStageId: this.currentStep,//所属阶段ID
                commentType: '6',//评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令
                roleInfo: this.detailInfo.roleInfo,//角色类型
                isCaptain: true,//是否队长发布（0:否 1:是）
                content: this.instruction,//评论内容
            }

            drillCommentPublishApi(params).then(res => {
                this.$message.success('发布成功')
                this.instruction = ''
            }).catch(error => {
                console.error('发布指令失败:', error)
                this.$message.error('发布失败，请重试')
            }).finally(() => {
                // 结束loading
                this.instructionLoading = false
            })
        },
        getMemberName(list = []) {
            return list.map(item => item.nickName).join('，')
        },
        // 获取小组成员名称
        getGroupMemberName(group) {
            if (!group || !group.groupMembers || group.groupMembers.length === 0) {
                return ''
            }
            return group.groupMembers.map(member => member.nickName).join('，')
        },
        // 传递setStep方法
        setStep(value) {
            this.$emit('setStep', value)
        },
        // 添加头像方法
        getAvatar(avatar) {
            return avatar ? process.env.VUE_APP_BASE_API + avatar : require("@/assets/images/profile.jpg");
        },

        // 获取未读数量（各类型）
        async getNoticeState() {
            try {
                const response = await getNotificationStatisticsApi({
                    drillTaskId: this.noticeParams.drillTaskId,
                    groupByType: true // 是否按类型统计未读数
                });

                if (response && response.code === 200) {
                    this.unreadCount = response.data.totalUnreadCount || 0;

                    // 更新各类型通知数量统计
                    if (response.data.unreadCountByType) {
                        const typeCount = response.data.unreadCountByType;
                        this.mentionCount = typeCount['1'] || 0;  // @通知
                        this.replyCount = typeCount['2'] || 0;    // 回复通知
                        this.likeCount = typeCount['3'] || 0;     // 点赞通知
                        this.systemCount = typeCount['4'] || 0;   // 系统通知
                    }
                }
            } catch (error) {
                console.error('获取通知统计失败:', error);
            }
        },

        // 打开通知弹窗
        openNotice() {
            this.noticeDialog = true;
            this.getNotificationList();
        },

        // 获取通知列表
        async getNotificationList() {
            if (!this.noticeParams.drillTaskId) {
                console.warn('drillTaskId is required for notification list');
                return;
            }

            try {
                this.noticeLoading = true;
                const response = await getNotificationListApi(this.noticeParams);

                if (response && response.code === 200) {
                    this.noticeList = response.data.records || [];
                    this.noticeTotal = response.data.total || 0;
                } else {
                    this.$message.error(response?.msg || '获取通知列表失败');
                }
                this.getNoticeState();// 获取未读数量
            } catch (error) {
                console.error('获取通知列表失败:', error);
                this.$message.error('获取通知列表失败');
            } finally {
                this.noticeLoading = false;
            }
        },

        // 标记通知为已读
        async markNotificationRead(notificationIds = null, notificationType = null) {
            if (!this.noticeParams.drillTaskId) {
                return;
            }

            try {
                const params = {
                    drillTaskId: this.noticeParams.drillTaskId,
                    notificationIds,
                    notificationType
                };

                const response = await markNotificationReadApi(params);

                if (response && response.code === 200) {
                    this.$message.success('标记已读成功');

                    // 刷新通知列表
                    this.getNotificationList();
                } else {
                    this.$message.error(response?.msg || '标记已读失败');
                }
            } catch (error) {
                console.error('标记已读失败:', error);
                this.$message.error('标记已读失败');
            }
        },

        // 标记所有通知为已读
        markAllRead() {
            this.markNotificationRead(null, this.noticeParams.notificationType);
        },

        // 处理 WebSocket 消息
        handleMessage({ channel, data }) {
            if (channel === 'DRILL_NOTIFICATION') {
                // 通知消息更新
                const { drillTaskId, unreadCount } = data;

                // 检查是否是当前演练任务的通知
                if (drillTaskId !== this.noticeParams.drillTaskId) {
                    return;
                }

                // 更新未读数量统计
                if (typeof unreadCount !== 'undefined') {
                    this.unreadCount = unreadCount;
                }

                // 如果通知弹窗打开，刷新通知列表
                if (this.noticeDialog) {
                    // this.getNotificationList(); //获取通知列表，目前和标记已读的回调有重复，考虑到这个列表没有实时需求，所有暂时不使用
                }
            }
        },

        // 获取通知类型颜色
        getNoticeTypeColor(type) {
            // 从配置中查找对应类型的通知
            const category = this.notificationCategories.find(
                cat => cat.type === type
            );

            return category && category.typeColor ? category.typeColor : 'info';
        },

        // 处理分页变化
        handlePageChange(page) {
            this.noticeParams.pageNum = page;
            this.getNotificationList();
        },

        // 重置筛选条件
        resetFilters() {
            this.noticeParams = {
                ...this.noticeParams,
                pageNum: 1,
                onlyUnread: null,
                notificationType: null,
                orderBy: 'createTime_desc'
            };
            this.getNotificationList();
        },

        // 设置筛选条件
        setFilter(onlyUnread, notificationType) {
            this.noticeParams.onlyUnread = onlyUnread; //是否只查询未读通知，true=仅未读，false=仅已读，null=全部
            this.noticeParams.notificationType = notificationType;
            this.noticeParams.pageNum = 1; // 重置页码
            this.getNotificationList();
        },

        // 处理下拉菜单命令
        handleFilterCommand(command) {
            // 从配置数组中获取对应的类型
            const category = this.notificationCategories.find(
                cat => cat.command === command
            );

            if (category) {
                this.setFilter(this.noticeParams.onlyUnread, category.type);
            }
        },

        // 获取分类数量
        getCategoryCount(command) {
            switch (command) {
                case 'all':
                    return this.unreadCount;
                case 'like':
                    return this.likeCount;
                case 'mention':
                    return this.mentionCount;
                case 'reply':
                    return this.replyCount;
                case 'system':
                    return this.systemCount;
                default:
                    return 0;
            }
        },

        // 获取当前筛选文本
        getCurrentFilterText() {
            // 根据当前选中的通知类型查找对应的分类配置
            const currentCategory = this.notificationCategories.find(
                category => category.type === this.noticeParams.notificationType
            );

            return currentCategory ? currentCategory.text : this.notificationCategories[0].text;
        },

        // 处理已读状态筛选命令
        handleReadStatusCommand(command) {
            // 从配置中查找对应的 onlyUnread 值
            const status = this.readStatusOptions.find(option => option.value === command);

            if (status) {
                this.setFilter(status.onlyUnread, this.noticeParams.notificationType);
            }
        },

        // 获取当前已读状态文本
        getCurrentReadStatusText() {
            // 直接根据当前的 onlyUnread 值从配置中查找对应的文本
            const status = this.readStatusOptions.find(
                option => option.onlyUnread === this.noticeParams.onlyUnread
            );

            return status ? status.text : this.readStatusOptions[0].text;
        },

        goCommentPlace(item) {
            // item = {
            //     "notificationId": "1947227505610190848",
            //     "drillTaskId": "1935862660367003648",
            //     "notificationType": 1,
            //     "notificationTypeName": "@通知",
            //     "fromUserId": "627",
            //     "fromUserName": "redCap",
            //     "fromUserNickName": "红队队长",
            //     "fromUserAvatar": "/file/public/query/1945307933324025857",
            //     "toUserId": "625",
            //     "relatedCommentId": "1937757465160646656",
            //     "relatedCommentReplyId": "1947227505601802240",
            //     "content": "在回复中@了您：在这里@蓝队队长 ",
            //     "extraData": "{\"mentionCount\": 1}",
            //     "isRead": 0,
            //     "readTime": null,
            //     "createdTime": "2025-07-21 17:29:50",
            //     "read": false
            // }

            // 检查必要的参数
            if (!item.relatedCommentId) {
                console.warn('缺少 relatedCommentId，无法定位评论');
                return;
            }

            // 关闭通知弹窗
            this.noticeDialog = false;

            // 通过事件总线通知 vsScreen 组件滚动到指定评论
            this.$root.$emit('scroll-to-comment', {
                commentId: item.relatedCommentId,
                commentReplyId: item.relatedCommentReplyId,
                notificationId: item.notificationId
            });

            // 标记通知为已读
            if (!item.isRead && item.notificationId) {
                this.markNotificationRead([item.notificationId]);
            }
        },
    }
}
</script>

<style scoped lang="scss">
.roleOption {
    .role-title {
        background-color: #FFFFFF;
        padding-top: 20px;
        display: flex;

        .role-info {
            flex: 1;

            img {
                height: 12px;
                margin-right: 10px;
            }

            .main-role {
                display: flex;
                align-items: center;
                font-size: 17px;
                font-weight: 600;
            }

            .other-role {
                margin: 15px 0 0 42px;
                font-size: 14px;
            }
        }

        .notice {
            background: url("../../../assets/images/simulatedVS/noticeIcon.svg") no-repeat;
            background-size: cover;
            width: 35px;
            height: 35px;
            position: relative;
            flex-shrink: 0;
            cursor: pointer;

            .notice-num {
                position: absolute;
                top: 2px;
                right: -5px;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                line-height: 20px;
                text-align: center;
                background-color: red;
                color: white;
                font-size: 11px;
            }
        }
    }

    .option-Tab {
        margin: 0 -20px;
        padding-top: 10px;
        background-color: #F4FBFF;
        display: flex;
        justify-content: space-around;
        align-content: center;

        .tab-item {
            font-weight: 600;
            cursor: pointer;
            font-size: 17px;
            padding-bottom: 10px;

            &:hover {
                color: #247CFF;
            }
        }

        .active-tab {
            color: #247CFF;
            border-bottom: 4px solid #247CFF;
        }
    }

    .option-Main {
        padding: 20px 0;

        .personInfo {
            .team-block {
                .team-title {
                    font-weight: 600;
                    font-size: 17px;

                    img {
                        height: 12px;
                        margin-right: 10px;
                    }
                }

                .person-list {
                    margin-top: 15px;
                    margin-left: 45px;
                    font-size: 14px;

                    .group-info {
                        margin-top: 15px;
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-radius: 4px;

                        .group-title {
                            font-weight: 600;
                            margin-bottom: 8px;
                            color: #333;
                        }

                        .group-details {
                            margin-left: 15px;
                            font-size: 13px;
                            color: #666;
                        }
                    }
                }
            }
        }
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .roleOption {
        /* 减小整体内边距 */
        padding: 5px;

        .role-title {
            padding-top: 10px;

            img {
                height: 10px;
                margin-right: 5px;
            }

            .main-role {
                font-size: 14px;
            }

            .other-role {
                margin: 8px 0 0 25px;
                font-size: 12px;
            }
        }

        .option-Tab {
            margin: 0 -10px;
            padding-top: 5px;
            flex-wrap: wrap;
            /* 允许在需要时换行 */

            .tab-item {
                font-size: 13px;
                padding: 5px 8px;
                margin-bottom: 5px;
                white-space: nowrap;
                /* 防止文本换行 */

                /* 减小底部边框宽度 */
                &.active-tab {
                    border-bottom-width: 2px;
                }
            }
        }

        .option-Main {
            padding: 10px 0;

            /* 调整人员信息区域 */
            .personInfo {
                .team-block {
                    margin-bottom: 10px !important;

                    .team-title {
                        font-size: 14px;

                        img {
                            height: 10px;
                            margin-right: 5px;
                        }
                    }

                    .person-list {
                        margin-top: 8px;
                        margin-left: 25px;
                        font-size: 12px;

                        div {
                            margin-bottom: 5px !important;
                        }

                        .group-info {
                            margin-top: 8px;
                            padding: 5px;

                            .group-title {
                                font-size: 12px;
                                margin-bottom: 5px;
                            }

                            .group-details {
                                margin-left: 10px;
                                font-size: 11px;
                            }
                        }
                    }
                }
            }

            /* 调整文本区域大小 */
            .el-textarea {
                ::v-deep .el-textarea__inner {
                    font-size: 12px;
                    padding: 5px;
                }
            }

            /* 调整按钮大小和间距 */
            .el-button {
                padding: 5px 10px;
                font-size: 12px;
                margin-left: 5px;
            }
        }
    }
}

.dialog__wrapper {
    ::v-deep .el-dialog__body {
        padding: 10px 20px 0px;
    }

    .notice-list {

        .notice-item {
            // font-size: 12px;
            background-color: #FFFFFF;
            display: flex;
            margin-bottom: 20px;

            >img {
                width: 40px;
                height: 40px;
                aspect-ratio: 1;
                flex-shrink: 0;
                border-radius: 50%;
            }

            .main-content {
                color: #000000;
                padding: 0 10px;
                flex: 1;

                .user-name {
                    color: #eb7350;
                    display: inline-block;
                    margin-right: 10px;
                }

                .content {
                    margin-bottom: 5px;
                    word-break: break-word;
                    white-space: pre-wrap;
                    display: inline;
                }

                .time {
                    color: #999999;
                }
            }
        }
    }
}

// 通知弹窗样式
.notice-dialog {
    .notice-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 16px;

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .filter-dropdown {
            .dropdown-trigger {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                cursor: pointer;
                background-color: #fff;
                font-size: 14px;
                color: #606266;
                min-width: 120px;

                &:hover {
                    border-color: #c0c4cc;
                }

                i {
                    margin-left: 8px;
                }
            }

            &.read-status-filter {
                .dropdown-trigger {
                    min-width: 100px;
                    background-color: #f8f9fa;
                    border-color: #e9ecef;

                    &:hover {
                        border-color: #adb5bd;
                    }
                }
            }
        }

        .header-actions {
            .el-button {
                color: #409eff;

                &:disabled {
                    color: #c0c4cc;
                }
            }
        }
    }

    .el-dropdown-menu {
        .el-dropdown-menu__item {
            display: flex;
            align-items: center;
            padding: 8px 16px;

            i {
                margin-right: 8px;
                font-size: 14px;
            }

            .count {
                margin-left: auto;
                background-color: #f56c6c;
                color: white;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 10px;
                min-width: 18px;
                text-align: center;
            }
        }
    }

    .notice-list {
        max-height: 400px;
        overflow-y: auto;

        .notice-item {
            display: flex;
            align-items: flex-start;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 8px;
            position: relative;
            transition: background-color 0.3s;

            &:hover {
                background-color: #f5f7fa;
            }

            &.unread {
                background-color: #f0f9ff;

                &::before {
                    content: '';
                    position: absolute;
                    top: 12px;
                    right: 12px;
                    width: 8px;
                    height: 8px;
                    background-color: #f56c6c;
                    border-radius: 50%;
                }
            }

            .notice-type-badge {
                margin-right: 8px;
                margin-top: 2px;
            }

            .user-avatar {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                margin-right: 12px;
                object-fit: cover;
            }

            .main-content {
                flex: 1;

                .user-name {
                    font-weight: 500;
                    color: #303133;
                    margin-bottom: 4px;
                }

                .content {
                    color: #606266;
                    line-height: 1.4;
                    margin-bottom: 4px;
                }

                .time {
                    color: #909399;
                    font-size: 12px;
                }
            }

            .notice-actions {
                margin-left: 8px;
            }
        }
    }

    .empty-notice {
        text-align: center;
        padding: 40px 0;
        color: #909399;

        i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
        }

        p {
            margin: 0;
            font-size: 14px;
        }
    }

    .notice-pagination {
        text-align: center;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #ebeef5;
    }
}
</style>